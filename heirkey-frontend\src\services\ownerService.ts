import api from './api';
import { Owner } from '@/types/owner';

class OwnerService {
  async getOwnerByUserId(userId: string): Promise<Owner> {
    const response: any = await api.get(`/v1/api/owners/user/${userId}`);
    return response.data.data.owner;
  }

  async getOwnerById(ownerId: string): Promise<Owner> {
    const response: any = await api.get(`/v1/api/owners/${ownerId}`);
    return response.data.data.owner;
  }

  async getOwnerByEmail(email: string): Promise<Owner> {
    const response: any = await api.get(`/v1/api/owners/email/${email}`);
    return response.data.data.owner;
  }
}

export default new OwnerService(); 