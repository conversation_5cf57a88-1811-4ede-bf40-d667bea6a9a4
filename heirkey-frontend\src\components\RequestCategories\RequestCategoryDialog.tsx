import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { useAppDispatch, useAppSelector } from "@/store";
import { requestCategories } from "@/store/slices/requestedCategoriesSlice";
import { CATEGORIES, CATEGORY_NAMES, CategoryId } from "@/constants/categories";
import { Users } from 'lucide-react';

interface RequestCategoryDialogProps {
  onRequestSent?: () => void;
}

export function RequestCategoryDialog({ onRequestSent }: RequestCategoryDialogProps) {
  const [open, setOpen] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState<CategoryId[]>([]);
  const [message, setMessage] = useState("");

  const dispatch = useAppDispatch();
  const { loading, error, success } = useAppSelector((state) => state.requestedCategories);

  const handleCategoryToggle = (categoryId: CategoryId) => {
    setSelectedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedCategories.length === 0) {
      toast.error("Please select at least one category");
      return;
    }

    const result = await dispatch(requestCategories({
      categoryIds: selectedCategories,
      message: message.trim() || undefined
    }));

    if (requestCategories.fulfilled.match(result)) {
      toast.success("Category access requested successfully!");
      setOpen(false);
      setSelectedCategories([]);
      setMessage("");
      onRequestSent?.();
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="font-medium flex items-center gap-2">
          <Users className="w-5 h-5 mr-2" />
          Request Category Access
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] w-[95vw] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl sm:text-2xl">Request Category Access</DialogTitle>
          <DialogDescription className="text-sm sm:text-base">
            Select the categories you want to request access to. You can add an optional message explaining your request.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <h4 className="text-sm sm:text-base font-medium">Select Categories</h4>
              <div className="flex flex-wrap gap-2">
                {Object.entries(CATEGORIES).map(([key, id]) => (
                  <Badge
                    key={id}
                    variant={selectedCategories.includes(id) ? "default" : "outline"}
                    className="cursor-pointer hover:bg-primary/10 text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-1.5"
                    onClick={() => handleCategoryToggle(id)}
                  >
                    {CATEGORY_NAMES[id]}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm sm:text-base font-medium">Message (Optional)</h4>
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Add a message explaining your request..."
                className="min-h-[100px] text-sm sm:text-base"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" disabled={loading} className="w-full sm:w-auto">
              {loading ? "Sending Request..." : "Send Request"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default RequestCategoryDialog; 