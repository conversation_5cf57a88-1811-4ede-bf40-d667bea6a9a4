import { useState, useEffect, useRef, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Label } from '@/components/ui/label';
import Header from '@/web/components/Layout/AppHeader';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, UserPen } from 'lucide-react';
import authService from '@/services/authService';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import Select from 'react-select';
import countryList from 'react-select-country-list';
import { useDispatch, useSelector } from "react-redux";
import { fetchOwnerSubscription, fetchPricingPlans } from "@/store/slices/subscriptionSlice";
import { RootState, AppDispatch } from "@/store";
import ownerService from '@/services/ownerService';

// Define the option type for react-select
interface CountryOption {
  value: string;
  label: string;
}

export default function WebUserProfile() {
  const { user, logout, isLoading, setUser } = useAuth();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [firstName, setFirstName] = useState(user?.firstName || '');
  const [lastName, setLastName] = useState(user?.lastName || '');
  const [email, setEmail] = useState(user?.email || '');
  const [username, setUsername] = useState(user?.username || '');
  const [phone, setPhone] = useState(user?.phone || '');
  const [address, setAddress] = useState(user?.address || '');
  const [zipCode, setZipCode] = useState(user?.zipCode || '');
  const [country, setCountry] = useState(user?.country || '');

  // Get country options using the countryList package
  const countryOptions = useMemo(() => countryList().getData(), []);
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(user?.image || null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // For real-time validation
  const [phoneError, setPhoneError] = useState<string | null>(null);
  const [zipCodeError, setZipCodeError] = useState<string | null>(null);

  const dispatch = useDispatch<AppDispatch>();
  const { currentSubscription, plans, loading: subLoading } = useSelector((state: RootState) => state.subscription);

  const [ownerId, setOwnerId] = useState<string | null>(null);
  const [ownerLoading, setOwnerLoading] = useState(true);

  useEffect(() => {
    if (user) {
      setFirstName(user.firstName || '');
      setLastName(user.lastName || '');
      setEmail(user.email || '');
      setUsername(user.username || '');
      setPhone(user.phone || '');
      setAddress(user.address || '');
      setZipCode(user.zipCode || '');
      setCountry(user.country || '');
      if (user.image) {
        setImagePreview(user.image);
      }
    }
  }, [user]);

  useEffect(() => {
    const fetchOwner = async () => {
      if (user?.id) {
        setOwnerLoading(true);
        try {
          // If user has an ownerId, use it directly (for nominees/keyholders)
          if (user.ownerId) {
            setOwnerId(user.ownerId);
          } else {
            // Otherwise, fetch the owner by userId (for owners)
            const owner = await ownerService.getOwnerByUserId(user.id);
            setOwnerId(owner._id);
          }
        } catch (err) {
          setOwnerId(null);
        } finally {
          setOwnerLoading(false);
        }
      }
    };
    fetchOwner();
  }, [user]);

  useEffect(() => {
    if (ownerId) {
      dispatch(fetchOwnerSubscription(ownerId));
    }
    dispatch(fetchPricingPlans());
  }, [dispatch, ownerId]);

  const subscribedPlan = plans.find(plan => plan._id === currentSubscription?.planId);

  // Cleanup timeouts on component unmount
  useEffect(() => {
    return () => {
      // Clear all pending timeouts when component unmounts
      Object.values(timeoutRef.current).forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setProfileImage(file);

      // Create a preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Validation functions
  const validatePhone = (value: string): boolean => {
    // With react-phone-input-2, the validation is handled by the component
    // We just need to check if the phone number has a minimum length
    const isValid = value && value.length >= 8 ? true : false;
    setPhoneError(isValid ? null : 'Please enter a valid phone number');
    return isValid;
  };

  const validateZipCode = (value: string): boolean => {
    // Basic ZIP code validation - can be enhanced based on country
    const zipRegex = /^[0-9a-zA-Z\s\-]{3,10}$/;
    const isValid = zipRegex.test(value);
    setZipCodeError(isValid ? null : 'Please enter a valid ZIP/postal code');
    return isValid;
  };

  // Create a reference for timeout IDs
  const timeoutRef = useRef<Record<string, NodeJS.Timeout>>({});

  // Debounced update function for real-time syncing
  const debouncedUpdate = (field: string, value: string) => {
    // Clear any existing timeout for this field
    if (timeoutRef.current[field]) {
      clearTimeout(timeoutRef.current[field]);
    }

    // Set a new timeout (700ms delay)
    timeoutRef.current[field] = setTimeout(async () => {
      try {
        const updateData: Record<string, string> = { [field]: value };
        const updatedUser = await authService.updateProfile(updateData);
        setUser(updatedUser);
      } catch (err) {
        console.error(`Error updating ${field}:`, err);
      }
    }, 700); // Wait for 700ms of inactivity before making the API call
  };

  // Handle field changes with validation and real-time update
  const handleFieldChange = (field: string, value: string) => {
    switch (field) {
      case 'phone':
        setPhone(value);
        if (validatePhone(value)) {
          debouncedUpdate(field, value);
        }
        break;
      case 'zipCode':
        setZipCode(value);
        if (validateZipCode(value)) {
          debouncedUpdate(field, value);
        }
        break;
      case 'address':
        setAddress(value);
        debouncedUpdate(field, value);
        break;
      case 'country':
        setCountry(value);
        debouncedUpdate(field, value);
        break;
      default:
        break;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isEditing) return;
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    // Validate fields
    const isPhoneValid = phone ? validatePhone(phone) : true;
    const isZipValid = zipCode ? validateZipCode(zipCode) : true;

    if (!isPhoneValid || !isZipValid) {
      setIsSubmitting(false);
      setError('Please correct the errors before submitting');
      return;
    }

    try {
      // First update the profile image if changed
      if (profileImage) {
        const formData = new FormData();
        formData.append('image', profileImage);
        const updatedUser = await authService.updateProfileImage(formData);
        setUser(updatedUser);
      }

      // Then update the profile information
      const updatedUser = await authService.updateProfile({
        firstName: firstName || undefined,
        lastName: lastName || undefined,
        username: username || undefined,
        phone: phone || undefined,
        address: address || undefined,
        zipCode: zipCode || undefined,
        country: country || undefined
      });

      setUser(updatedUser);
      setSuccess('Profile updated successfully!');

      setIsEditing(false);
      setTimeout(() => {
        navigate('/dashboard');
      }, 1500);
    } catch (err: unknown) {
      console.error('Profile update error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update profile';
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get initials for avatar fallback
  const getInitials = () => {
    if (firstName && lastName) {
      return `${firstName[0]}${lastName[0]}`.toUpperCase();
    } else if (username) {
      return username.substring(0, 2).toUpperCase();
    }
    return 'UN';
  };

  return (
    <div className="min-h-screen bg-[#f8f9fb]">
      <Header />
      {/* Welcome Banner */}
      <div className="w-full bg-gradient-to-r from-[#1F2668] to-[#22BBCC] text-white">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold">Welcome, {firstName || username || 'User'}</h1>
        </div>
      </div>
      <div className="max-w-7xl mx-auto py-4 px-4 flex flex-col lg:flex-row gap-6">
        <div className="flex-1">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-gray-900">Profile Settings</h2>
              {!isEditing && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => setIsEditing(true)}
                  disabled={isLoading}
                >
                  <UserPen size={14} /> Edit
                </Button>
              )}
            </div>

            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="mb-4 bg-green-50 border-green-200">
                <AlertDescription className="text-green-700">{success}</AlertDescription>
              </Alert>
            )}

            <form className="space-y-5" onSubmit={handleSubmit}>
              <div className="flex items-center gap-6 mb-2">
                <div className="relative group">
                  <Avatar className="h-20 w-20 border-2 border-[#22BBCC] shadow">
                    {imagePreview ? (
                      <AvatarImage src={imagePreview} alt="Profile" className="object-cover" />
                    ) : null}
                    <AvatarFallback>{getInitials()}</AvatarFallback>
                  </Avatar>
                  <div className="absolute inset-0 flex items-center border border-[#22BBCC] shadow-md justify-center bg-black bg-opacity-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    {isEditing && (
                      <Label htmlFor="profileImage" className="cursor-pointer text-white text-xs font-medium">
                        Change
                      </Label>
                    )}
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-medium">{firstName} {lastName}</h3>
                  <p className="text-sm text-gray-500">{email}</p>
                  {isEditing && (
                    <Input
                      id="profileImage"
                      name="profileImage"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleImageChange}
                    />
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">First Name</label>
                  <Input
                    id="firstName"
                    name="firstName"
                    type="text"
                    placeholder="First name"
                    className="h-10"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    readOnly={!isEditing}
                  />
                </div>

                <div className="space-y-1">
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">Last Name</label>
                  <Input
                    id="lastName"
                    name="lastName"
                    type="text"
                    placeholder="Last name"
                    className="h-10"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    readOnly={!isEditing}
                  />
                </div>
              </div>

              <div className="space-y-1">
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700">Phone Number</label>
                <PhoneInput
                  country={'us'}
                  value={phone}
                  onChange={(value) => isEditing && handleFieldChange('phone', value)}
                  inputProps={{
                    name: 'phone',
                    id: 'phone',
                    required: true,
                    readOnly: !isEditing,
                    disabled: !isEditing,
                  }}
                  containerClass={`${phoneError ? 'border-red-500' : ''}`}
                  inputClass="w-full h-18"
                  buttonClass="h-18"
                  containerStyle={{ height: '35px' }}
                  disabled={!isEditing}
                />
                {phoneError && <p className="text-xs text-red-500 mt-1">{phoneError}</p>}
              </div>

              <div className="space-y-1">
                <label htmlFor="address" className="block text-sm font-medium text-gray-700">Address</label>
                <Input
                  id="address"
                  name="address"
                  type="text"
                  placeholder="Full address"
                  className="h-10"
                  value={address}
                  onChange={(e) => handleFieldChange('address', e.target.value)}
                  readOnly={!isEditing}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700">ZIP/Postal Code</label>
                  <Input
                    id="zipCode"
                    name="zipCode"
                    type="text"
                    placeholder="ZIP/postal code"
                    className={`h-10 ${zipCodeError ? 'border-red-500' : ''}`}
                    value={zipCode}
                    onChange={(e) => handleFieldChange('zipCode', e.target.value)}
                    onBlur={(e) => validateZipCode(e.target.value)}
                    readOnly={!isEditing}
                  />
                  {zipCodeError && <p className="text-xs text-red-500 mt-1">{zipCodeError}</p>}
                </div>

                <div className="space-y-1">
                  <label htmlFor="country" className="block text-sm font-medium text-gray-700">Country</label>
                  <Select<CountryOption>
                    id="country"
                    name="country"
                    options={countryOptions}
                    placeholder="Select country"
                    className="h-10"
                    classNamePrefix="select"
                    value={countryOptions.find((option: CountryOption) => option.label === country) || null}
                    onChange={isEditing ? (selectedOption: CountryOption | null) => {
                      if (selectedOption) {
                        handleFieldChange('country', selectedOption.label);
                      }
                    } : undefined}
                    isDisabled={!isEditing}
                    styles={{
                      control: (baseStyles: any) => ({
                        ...baseStyles,
                        height: '40px',
                        minHeight: '40px',
                        borderColor: '#e2e8f0',
                        backgroundColor: !isEditing ? '#f3f4f6' : 'white',
                      }),
                      valueContainer: (base: any) => ({
                        ...base,
                        padding: '0 8px',
                      }),
                      menu: (baseStyles: any) => ({
                        ...baseStyles,
                        zIndex: 50,
                      }),
                    }}
                  />
                </div>
              </div>

              <div className="flex justify-end pt-2">
                {isEditing && (
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      className="h-10 px-6"
                      onClick={() => {
                        setIsEditing(false);
                        setFirstName(user?.firstName || '');
                        setLastName(user?.lastName || '');
                        setEmail(user?.email || '');
                        setUsername(user?.username || '');
                        setPhone(user?.phone || '');
                        setAddress(user?.address || '');
                        setZipCode(user?.zipCode || '');
                        setCountry(user?.country || '');
                        setImagePreview(user?.image || null);
                        setProfileImage(null);
                        setError(null);
                        setSuccess(null);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="h-10 px-6 bg-[#22BBCC] text-white hover:bg-[#22BBCA] transition-colors duration-200"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...
                        </>
                      ) : (
                        "Update Profile"
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </form>

            {/* Subscription Section */}
            <div className="bg-white rounded-lg shadow-sm p-6 mt-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">Subscription</h2>
              {ownerLoading || subLoading ? (
                <div className="text-gray-500">Loading subscription...</div>
              ) : currentSubscription ? (
                subscribedPlan ? (
                  <div>
                    <p className="mb-2"><strong>Plan:</strong> {subscribedPlan.type}</p>
                    <p className="mb-2"><strong>Price:</strong> {subscribedPlan.displayPrice}</p>
                    <p className="mb-2"><strong>Status:</strong> {currentSubscription.isActive ? "Active" : "Inactive"}</p>
                    <p className="mb-2"><strong>Start Date:</strong> {currentSubscription.createdAt ? new Date(currentSubscription.createdAt).toLocaleDateString() : "N/A"}</p>
                    <p className="mb-2"><strong>End Date:</strong> {currentSubscription.expiryAt ? new Date(currentSubscription.expiryAt).toLocaleDateString() : "N/A"}</p>
                    <div>
                      <strong>Features:</strong>
                      <ul className="list-disc ml-6">
                        {subscribedPlan.features.map((feature, idx) => (
                          <li key={idx}>{feature}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ) : (
                  <div>Subscribed plan details not found.</div>
                )
              ) : (
                <div>No active subscription.</div>
              )}
            </div>
          </div>
        </div>
        <div className="w-full lg:w-[350px] flex-shrink-0">
          <SearchPanel />
        </div>
      </div>
    </div>
  );
}