import { Request, Response } from 'express';
import PricingPlan from '../models/PricingPlan';
import mongoose from 'mongoose';

// Create a new pricing plan
export const createPricingPlan = async (req: Request, res: Response): Promise<void> => {
  try {
    const { type, price, displayPrice, tagline, features, duration, active } = req.body;

    // Validate required fields
    if (!type || price === undefined || !displayPrice || !tagline || !features) {
      res.status(400).json({
        message: 'Type, price, displayPrice, tagline, and features are required.'
      });
      return;
    }

    // Check if pricing plan with this type already exists
    const existingPlan = await PricingPlan.findOne({ type });
    if (existingPlan) {
      res.status(400).json({
        message: `Pricing plan with type '${type}' already exists.`
      });
      return;
    }

    // Set default duration based on plan type if not provided
    let planDuration = duration;
    if (planDuration === undefined) {
      planDuration = type === 'temporary_key' ? -1 : 1; // Infinite for temporary, 1 month for others
    }

    const pricingPlan = new PricingPlan({
      type,
      price,
      displayPrice,
      tagline,
      features,
      duration: planDuration,
      active: active !== undefined ? active : true
    });

    await pricingPlan.save();
    res.status(201).json(pricingPlan);
  } catch (error) {
    console.error('Error creating pricing plan:', error);
    res.status(500).json({
      message: 'Error creating pricing plan',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all pricing plans
export const getAllPricingPlans = async (req: Request, res: Response): Promise<void> => {
  try {
    const { active } = req.query;
    
    // Build filter object
    const filter: any = {};
    if (active !== undefined) {
      filter.active = active === 'true';
    }

    const pricingPlans = await PricingPlan.find(filter).sort({ price: 1 });
    res.status(200).json(pricingPlans);
  } catch (error) {
    console.error('Error fetching pricing plans:', error);
    res.status(500).json({ 
      message: 'Error fetching pricing plans', 
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get a specific pricing plan by ID
export const getPricingPlanById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid pricing plan ID.' });
      return;
    }

    const pricingPlan = await PricingPlan.findById(id);
    if (!pricingPlan) {
      res.status(404).json({ message: 'Pricing plan not found.' });
      return;
    }

    res.status(200).json(pricingPlan);
  } catch (error) {
    console.error('Error fetching pricing plan:', error);
    res.status(500).json({ 
      message: 'Error fetching pricing plan', 
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get a pricing plan by type
export const getPricingPlanByType = async (req: Request, res: Response): Promise<void> => {
  try {
    const { type } = req.params;

    const validTypes = ['temporary_key', 'spare_key', 'all_access_key'];
    if (!validTypes.includes(type)) {
      res.status(400).json({ 
        message: `Invalid pricing plan type. Must be one of: ${validTypes.join(', ')}` 
      });
      return;
    }

    const pricingPlan = await PricingPlan.findOne({ type });
    if (!pricingPlan) {
      res.status(404).json({ message: `Pricing plan with type '${type}' not found.` });
      return;
    }

    res.status(200).json(pricingPlan);
  } catch (error) {
    console.error('Error fetching pricing plan by type:', error);
    res.status(500).json({ 
      message: 'Error fetching pricing plan', 
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Update a pricing plan
export const updatePricingPlan = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const updates = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid pricing plan ID.' });
      return;
    }

    // If type is being updated, check for conflicts
    if (updates.type) {
      const existingPlan = await PricingPlan.findOne({ 
        type: updates.type, 
        _id: { $ne: id } 
      });
      if (existingPlan) {
        res.status(400).json({ 
          message: `Another pricing plan with type '${updates.type}' already exists.` 
        });
        return;
      }
    }

    const pricingPlan = await PricingPlan.findByIdAndUpdate(
      id, 
      updates, 
      { new: true, runValidators: true }
    );

    if (!pricingPlan) {
      res.status(404).json({ message: 'Pricing plan not found.' });
      return;
    }

    res.status(200).json(pricingPlan);
  } catch (error) {
    console.error('Error updating pricing plan:', error);
    res.status(500).json({ 
      message: 'Error updating pricing plan', 
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete a pricing plan
export const deletePricingPlan = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      res.status(400).json({ message: 'Invalid pricing plan ID.' });
      return;
    }

    const pricingPlan = await PricingPlan.findByIdAndDelete(id);
    if (!pricingPlan) {
      res.status(404).json({ message: 'Pricing plan not found.' });
      return;
    }

    res.status(200).json({ 
      message: 'Pricing plan deleted successfully',
      deletedPlan: pricingPlan
    });
  } catch (error) {
    console.error('Error deleting pricing plan:', error);
    res.status(500).json({ 
      message: 'Error deleting pricing plan', 
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Initialize default pricing plans
// Bulk update all existing plans with duration
export const updateAllPlansWithDuration = async (_req: Request, res: Response): Promise<void> => {
  try {
    // Update temporary_key plans to have infinite duration (-1)
    const tempKeyUpdate = await PricingPlan.updateMany(
      { type: 'temporary_key', $or: [{ duration: { $exists: false } }, { duration: null }] },
      { $set: { duration: -1 } }
    );

    // Update all other plans to have 1 month duration
    const otherPlansUpdate = await PricingPlan.updateMany(
      {
        type: { $in: ['spare_key', 'all_access_key'] },
        $or: [{ duration: { $exists: false } }, { duration: null }]
      },
      { $set: { duration: 1 } }
    );

    // Get all updated plans to return
    const allPlans = await PricingPlan.find().sort({ type: 1 });

    res.status(200).json({
      message: 'All pricing plans updated with duration successfully',
      updates: {
        temporaryKeyPlans: tempKeyUpdate.modifiedCount,
        otherPlans: otherPlansUpdate.modifiedCount,
        totalUpdated: tempKeyUpdate.modifiedCount + otherPlansUpdate.modifiedCount
      },
      plans: allPlans
    });
  } catch (error) {
    console.error('Error updating plans with duration:', error);
    res.status(500).json({
      message: 'Error updating plans with duration',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Migration function specifically for category limits
export const updateAllPlansWithCategoryLimits = async (_req: Request, res: Response): Promise<void> => {
  try {
    // Update temporary_key plans to have 1 category limit
    const tempKeyUpdate = await PricingPlan.updateMany(
      { type: 'temporary_key', $or: [{ categorylimit: { $exists: false } }, { categorylimit: null }] },
      { $set: { categorylimit: 1 } }
    );

    // Update all other plans to have unlimited categories (-1)
    const otherPlansUpdate = await PricingPlan.updateMany(
      {
        type: { $in: ['spare_key', 'all_access_key'] },
        $or: [{ categorylimit: { $exists: false } }, { categorylimit: null }]
      },
      { $set: { categorylimit: -1 } }
    );

    // Get all updated plans to return
    const allPlans = await PricingPlan.find().sort({ type: 1 });

    res.status(200).json({
      message: 'Plans updated with category limits successfully',
      updates: {
        temporaryKeyPlans: tempKeyUpdate.modifiedCount,
        otherPlans: otherPlansUpdate.modifiedCount,
        totalUpdated: tempKeyUpdate.modifiedCount + otherPlansUpdate.modifiedCount
      },
      plans: allPlans
    });

  } catch (error) {
    console.error('Error updating plans with category limits:', error);
    res.status(500).json({
      message: 'Error updating plans with category limits',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export const initializeDefaultPricingPlans = async (_req: Request, res: Response): Promise<void> => {
  try {
    // Check if pricing plans already exist
    const existingPlans = await PricingPlan.find();

    if (existingPlans.length === 0) {
      const defaultPlans = [
        {
          type: 'temporary_key',
          price: 0,
          displayPrice: 'Free to try',
          tagline: 'Try Heirkey risk free.',
          features: [
            'You can select one of categories that you would like to share with a designated key holder.',
            'Limited in the information you can share.',
            'The plan lets you try Heirkey\'s feature Risk-free.'
          ],
          duration: -1, // Infinite duration for temporary plan
          categorylimit: 1, // Limited to 1 category
          active: true
        },
        {
          type: 'spare_key',
          price: 10,
          displayPrice: '$10/mth',
          tagline: 'Limited access for a key holders',
          features: [
            'You can access and edit your information at any time, but your key holder\'s information is limited.',
            'You key holder can only access your information in the event of the owner\'s incapacity/death.',
            'Provides more privacy for the owner.'
          ],
          duration: 1, // 1 month duration
          categorylimit: -1, // Unlimited categories
          active: true
        },
        {
          type: 'all_access_key',
          price: 12,
          displayPrice: '$12/mth',
          tagline: 'Full access for your key holder.',
          features: [
            'You and your designated key holder can access your information at any time.',
            'Makes it easy for family members to share information.',
            'Great for universal use between family members.'
          ],
          duration: 1, // 1 month duration
          categorylimit: -1, // Unlimited categories
          active: true
        }
      ];

      const createdPlans = await PricingPlan.insertMany(defaultPlans);
      res.status(201).json({
        message: 'Default pricing plans created successfully',
        plans: createdPlans
      });
    } else {
      res.status(200).json({
        message: 'Pricing plans already exist',
        plans: existingPlans
      });
    }
  } catch (error) {
    console.error('Error initializing default pricing plans:', error);
    res.status(500).json({
      message: 'Error initializing default pricing plans',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
