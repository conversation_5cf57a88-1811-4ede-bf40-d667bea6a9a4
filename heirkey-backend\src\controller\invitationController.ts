import { Request, Response } from 'express';
import crypto from 'crypto';
import User from '../models/User';
import Owner from '../models/Owner';
import Role from '../models/Role';
import InvitedUser from '../models/InvitedUser';
import { AuthRequest } from '../middleware/authMiddleware';
import { IInviteUserData, InvitationStatus } from '../types/InvitedUser';
import { RoleType } from '../types/Role';
import sendEmail from '../utils/email';
import { CustomError } from '../utils/customError';

export const inviteUser = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { name, email, relation, phone } = req.body as IInviteUserData;
        const userId = req.user?.id;

        if (!userId) {
            res.status(401).json({ message: 'Unauthorized' });
            return;
        }

        // Find the owner making the invitation
        const owner = await Owner.findOne({ userId });
        if (!owner) {
            res.status(404).json({ message: 'Owner not found' });
            return;
        }

        // Check if user with this email already exists
        const existingUser = await User.findOne({ email: email.toLowerCase() });
        if (existingUser) {
            // Check if there's already a pending invitation for this user from this owner
            const existingInvitation = await InvitedUser.findOne({
                ownerId: owner._id,
                invitedUserId: existingUser._id,
                status: InvitationStatus.PENDING
            });

            if (existingInvitation) {
                res.status(400).json({ message: 'Invitation already sent to this email' });
                return;
            } else {
                res.status(400).json({ message: 'User with this email already exists' });
                return;
            }
        }

        // Generate invitation token
        const invitationToken = crypto.randomBytes(32).toString('hex');
        const hashedToken = crypto.createHash('sha256').update(invitationToken).digest('hex');

        // Create user record first (without password)
        const newUser = new User({
            firstName: name.split(' ')[0],
            lastName: name.split(' ').slice(1).join(' ') || '',
            email: email.toLowerCase(),
            phone,
            ownerId: owner._id, // Set owner reference for invited users
            externalUser: false
        });

        await newUser.save();

        // Create invitation record
        const invitation = new InvitedUser({
            ownerId: owner._id,
            invitedUserId: newUser._id,
            relation,
            status: InvitationStatus.PENDING,
            invitationToken: hashedToken,
            invitationTokenExpire: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
        });

        await invitation.save();

        // Send invitation email
        const invitationUrl = `${process.env.FRONTEND_URL}/accept-invitation/${invitationToken}`;
        const emailMessage = `
            <h2>You've been invited to join Heirkey</h2>
            <p>Hello ${name},</p>
            <p>You have been invited by ${owner.getFullName()} to join Heirkey as a ${relation}.</p>
            <p>Click the link below to accept the invitation and set up your account:</p>
            <a href="${invitationUrl}" style="background-color: #4CAF50; color: white; padding: 14px 20px; text-decoration: none; display: inline-block; border-radius: 4px;">Accept Invitation</a>
            <p>This invitation will expire in 24 hours.</p>
            <p>If you didn't expect this invitation, you can safely ignore this email.</p>
        `;

        await sendEmail({
            to: email,
            subject: 'Invitation to join Heirkey',
            html: emailMessage
        });

        res.status(201).json({
            message: 'Invitation sent successfully',
            invitation: {
                id: invitation._id,
                email,
                relation,
                status: invitation.status,
                createdAt: invitation.createdAt
            },
            // For testing purposes - remove in production
            debugToken: invitationToken
        });

    } catch (error) {
        console.error('Invitation error:', error);
        res.status(500).json({
            message: 'Error sending invitation',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

export const getInvitations = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?.id;

        if (!userId) {
            res.status(401).json({ message: 'Unauthorized' });
            return;
        }

        // Find the owner
        const owner = await Owner.findOne({ userId });
        if (!owner) {
            res.status(404).json({ message: 'Owner not found' });
            return;
        }

        // Get all invitations sent by this owner
        const invitations = await InvitedUser.findByOwnerId(owner._id);

        res.status(200).json({
            message: 'Invitations retrieved successfully',
            invitations: invitations.map(inv => ({
                id: inv._id,
                email: inv.invitedUser?.email,
                name: `${inv.invitedUser?.firstName || ''} ${inv.invitedUser?.lastName || ''}`.trim(),
                relation: inv.relation,
                status: inv.status,
                createdAt: inv.createdAt,
                updatedAt: inv.updatedAt
            }))
        });

    } catch (error) {
        console.error('Get invitations error:', error);
        res.status(500).json({
            message: 'Error retrieving invitations',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

export const acceptInvitation = async (req: Request, res: Response): Promise<void> => {
    try {
        const { token, password } = req.body;

        // Hash the token to match stored version
        const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

        // Find invitation by token
        const invitation = await InvitedUser.findOne({
            invitationToken: hashedToken,
            invitationTokenExpire: { $gt: new Date() },
            status: InvitationStatus.PENDING
        }).populate('invitedUser').populate('owner');

        if (!invitation) {
            res.status(400).json({ message: 'Invalid or expired invitation token' });
            return;
        }

        const user = invitation.invitedUser;
        const owner = invitation.owner;

        // Determine role based on relation
        let roleType: RoleType;
        const relationLower = invitation.relation.toLowerCase();
        if (relationLower.includes('family') || relationLower.includes('spouse') || relationLower.includes('child')) {
            roleType = RoleType.FAMILY;
        } else {
            roleType = RoleType.NOMINEE;
        }

        // Find the appropriate role
        const role = await Role.findOne({ name: roleType });
        if (!role) {
            res.status(500).json({ message: `${roleType} role not found` });
            return;
        }

        // Update user with password and role
        user.password = password;
        user.roleId = role._id;
        await user.save();

        // Update invitation status
        invitation.status = InvitationStatus.ACCEPTED;
        invitation.invitationToken = undefined;
        invitation.invitationTokenExpire = undefined;
        await invitation.save();

        res.status(200).json({
            message: 'Invitation accepted successfully',
            user: {
                id: user._id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: roleType,
                relation: invitation.relation
            }
        });

    } catch (error) {
        console.error('Accept invitation error:', error);
        res.status(500).json({
            message: 'Error accepting invitation',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
