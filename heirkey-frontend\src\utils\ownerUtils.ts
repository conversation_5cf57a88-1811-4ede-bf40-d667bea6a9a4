import ownerService from '@/services/ownerService';
import { User } from '@/services/authService';

/**
 * Gets the owner ID for a given user
 * Handles both owner users (who have an Owner record) and nominee/family users (who have ownerId reference)
 * @param user - The user object
 * @returns Promise<string | null> - The owner ID or null if not found
 */
export const getOwnerIdFromUser = async (user: User): Promise<string | null> => {
  try {
    if (!user?.id) {
      return null;
    }

    // First check if the user has an ownerId field (nominee/family user)
    if (user.ownerId) {
      console.log('User has ownerId reference:', user.ownerId);
      // Verify the owner exists by fetching it
      try {
        await ownerService.getOwnerById(user.ownerId);
        return user.ownerId;
      } catch (ownerFetchError) {
        console.warn('ownerId exists but owner record not found:', user.ownerId);
        return null;
      }
    }

    // If no ownerId, try to find an Owner record where this user is the owner
    try {
      const owner = await ownerService.getOwnerByUserId(user.id);
      console.log('Found owner record for user:', owner._id);
      return owner._id || null;
    } catch (ownerError) {
      console.log('No owner record found for user, user might be nominee/family without ownerId field');
      return null;
    }
  } catch (error) {
    console.error('Error getting owner ID from user:', error);
    return null;
  }
};

/**
 * Gets the owner ID for the current user with caching
 * @param user - The user object
 * @returns Promise<string | null> - The owner ID or null if not found
 */
let ownerIdCache: { [userId: string]: string | null } = {};

export const getCachedOwnerIdFromUser = async (user: User): Promise<string | null> => {
  try {
    if (!user?.id) {
      return null;
    }
    
    // Check cache first
    if (ownerIdCache[user.id] !== undefined) {
      return ownerIdCache[user.id];
    }
    
    // Fetch and cache
    const ownerId = await getOwnerIdFromUser(user);
    ownerIdCache[user.id] = ownerId;
    
    return ownerId;
  } catch (error) {
    console.error('Error getting cached owner ID from user:', error);
    return null;
  }
};

/**
 * Clears the owner ID cache
 */
export const clearOwnerIdCache = () => {
  ownerIdCache = {};
};
