import { Request, Response } from 'express';
import { Question } from '../models/Question';
import UserInput from '../models/userInput';
import User from '../models/User';
import mongoose from 'mongoose';

export const createQuestion = async (req: Request, res: Response) => {
    try {
        const question = await Question.create(req.body);
        res.status(201).json(question);
    } catch (error: any) {
        res.status(400).json({ message: error.message });
    }
};


export const getQuestions = async (req: Request, res: Response) => {
  try {
    console.log(req.query)
    const questions = await Question.find();
    res.status(200).json(questions);
  } catch (error: unknown) {
    if (error instanceof Error) {
      res.status(500).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'An unknown error occurred' });
    }
  }
};

export const getQuestionById = async (req: Request, res: Response) => {
  try {
    const question = await Question.findById(req.params.id);
    if (!question) {
      return res.status(404).json({ error: 'Question not found' });
    }
    res.status(200).json(question);
  } catch (error: unknown) {
    if (error instanceof Error) {
      res.status(500).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'An unknown error occurred' });
    }
  }
};

export const updateQuestion = async (req: Request, res: Response) => {
  try {
    const question = await Question.findByIdAndUpdate(req.params.id, req.body, { new: true });
    if (!question) {
      return res.status(404).json({ error: 'Question not found' });
    }
    res.status(200).json(question);
  } catch (error: unknown) {
    if (error instanceof Error) {
      res.status(400).json({ error: error.message });
    } else {
      res.status(400).json({ error: 'An unknown error occurred' });
    }
  }
};

export const deleteQuestion = async (req: Request, res: Response) => {
  try {
    const question = await Question.findByIdAndDelete(req.params.id);
    if (!question) {
      return res.status(404).json({ error: 'Question not found' });
    }
    res.status(200).json({ message: 'Question deleted successfully' });
  } catch (error: unknown) {
    if (error instanceof Error) {
      res.status(500).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'An unknown error occurred' });
    }
  }
};

export const getQuestionsByCategory = async (req: Request, res: Response) => {
  try {
    const { categoryId } = req.params;

    if (!categoryId) {
      return res.status(400).json({ error: 'Category ID is required' });
    }

    console.log(`Received request for questions with categoryId: ${categoryId}`);

    // Try to find the category using multiple strategies
    let category;
    let query;

    try {
      // Strategy 1: If categoryId is a valid ObjectId, use it directly
      if (mongoose.Types.ObjectId.isValid(categoryId)) {
        console.log(`Trying to find category by ObjectId: ${categoryId}`);
        const objId = new mongoose.Types.ObjectId(categoryId);
        category = await mongoose.model('Category').findById(objId);
        if (category) {
          console.log(`Found category by ObjectId: ${category.name}`);
          query = { categoryId: objId };
        }
      }

      // Strategy 2: If not found by ObjectId, try to find by name
      if (!category) {
        console.log(`Trying to find category by name containing: ${categoryId}`);
        category = await mongoose.model('Category').findOne({
          name: new RegExp(categoryId, 'i')
        });
        if (category) {
          console.log(`Found category by name: ${category.name}`);
          query = { categoryId: category._id };
        }
      }

      // Strategy 3: If numeric ID, try to find by numericId field
      if (!category && /^\d+$/.test(categoryId)) {
        console.log(`Trying to find category by numericId: ${categoryId}`);
        const numericId = parseInt(categoryId, 10);
        category = await mongoose.model('Category').findOne({ numericId });
        if (category) {
          console.log(`Found category by numericId: ${category.name}`);
          query = { categoryId: category._id };
        } else {
          // Fallback: Try to find by index position
          console.log(`Trying to find category by index position: ${categoryId}`);
          const allCategories = await mongoose.model('Category').find().sort({ createdAt: 1 });
          if (numericId > 0 && numericId <= allCategories.length) {
            category = allCategories[numericId - 1];
            if (category) {
              console.log(`Found category by index: ${category.name}`);
              query = { categoryId: category._id };
            }
          }
        }
      }
    } catch (error) {
      console.error('Error finding category:', error);
    }

    if (!query) {
      console.log(`Category not found for ID: ${categoryId}`);
      return res.status(404).json({ error: 'Category not found' });
    }

    const questions = await Question.find(query);

    res.status(200).json(questions);
  } catch (error: unknown) {
    if (error instanceof Error) {
      res.status(500).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'An unknown error occurred' });
    }
  }
};

export const getQuestionsBySubCategory = async (req: Request, res: Response) => {
  try {
    const { subCategoryId } = req.params;

    if (!subCategoryId) {
      return res.status(400).json({ error: 'Subcategory ID is required' });
    }

    console.log(`Received request for questions with subCategoryId: ${subCategoryId}`);

    // Try to find the subcategory using multiple strategies
    let subcategory;
    let query;

    try {
      // Strategy 1: If subCategoryId is a valid ObjectId, use it directly
      if (mongoose.Types.ObjectId.isValid(subCategoryId)) {
        console.log(`Trying to find subcategory by ObjectId: ${subCategoryId}`);
        const objId = new mongoose.Types.ObjectId(subCategoryId);
        subcategory = await mongoose.model('Subcategory').findById(objId);
        if (subcategory) {
          console.log(`Found subcategory by ObjectId: ${subcategory.name}`);
          query = { subCategoryId: objId };
        }
      }

      // Strategy 2: If not found by ObjectId, try to find by name
      if (!subcategory) {
        console.log(`Trying to find subcategory by name containing: ${subCategoryId}`);
        subcategory = await mongoose.model('Subcategory').findOne({
          name: new RegExp(subCategoryId, 'i')
        });
        if (subcategory) {
          console.log(`Found subcategory by name: ${subcategory.name}`);
          query = { subCategoryId: subcategory._id };
        }
      }

      // Strategy 3: If numeric ID, try to find by index position
      if (!subcategory && /^\d+$/.test(subCategoryId)) {
        console.log(`Trying to find subcategory by numeric index: ${subCategoryId}`);
        const numericId = parseInt(subCategoryId, 10);
        // Find all subcategories and get the one at the specified index (1-based)
        const allSubcategories = await mongoose.model('Subcategory').find().sort({ createdAt: 1 });
        if (numericId > 0 && numericId <= allSubcategories.length) {
          subcategory = allSubcategories[numericId - 1];
          if (subcategory) {
            console.log(`Found subcategory by index: ${subcategory.name}`);
            query = { subCategoryId: subcategory._id };
          }
        }
      }
    } catch (error) {
      console.error('Error finding subcategory:', error);
    }

    if (!query) {
      console.log(`Subcategory not found for ID: ${subCategoryId}`);
      return res.status(404).json({ error: 'Subcategory not found' });
    }

    const questions = await Question.find(query);

    res.status(200).json(questions);
  } catch (error: unknown) {
    if (error instanceof Error) {
      res.status(500).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'An unknown error occurred' });
    }
  }
};

export const saveAnswers = async (req: Request, res: Response) => {
  try {
    const { categoryId, subCategoryId, answersBySection } = req.body;

    if (!categoryId || !subCategoryId || !answersBySection) {
      return res.status(400).json({
        error: 'Category ID, Subcategory ID, and answers are required'
      });
    }

    // Get user ID from auth middleware
    let userId;

    if (req.user) {
      // If user is authenticated via Passport (req.user is set)
      userId = req.user && ('_id' in req.user ? req.user._id : 'id' in req.user ? req.user.id : undefined);
      console.log('User authenticated via Passport:', userId);
    } else if (req.body.userId) {
      // If userId is provided in the request body
      userId = req.body.userId;
      console.log('User ID from request body:', userId);
    } else {
      console.log('Authentication failed. User object:', req.user);
      return res.status(401).json({ error: 'User not authenticated' });
    }

    console.log('Using user ID for saving answers:', userId);

    // Validate and convert IDs to ObjectId
    let userIdObj, categoryIdObj, subCategoryIdObj;

    try {
      // Validate userId
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        console.error('Invalid userId format:', userId);
        return res.status(400).json({ error: 'Invalid user ID format' });
      }
      userIdObj = new mongoose.Types.ObjectId(userId);

      // For categoryId, we need to handle numeric IDs differently
      if (/^\d+$/.test(categoryId)) {
        // If it's a numeric ID, find the category by numericId
        console.log('Looking up category by numericId:', categoryId);
        const category = await mongoose.model('Category').findOne({ numericId: parseInt(categoryId, 10) });
        if (!category) {
          console.error('Category not found with numericId:', categoryId);
          return res.status(404).json({ error: 'Category not found' });
        }
        categoryIdObj = category._id;
        console.log('Found category:', category.name, 'with ID:', categoryIdObj);
      } else if (mongoose.Types.ObjectId.isValid(categoryId)) {
        // If it's a valid ObjectId, use it directly
        categoryIdObj = new mongoose.Types.ObjectId(categoryId);
      } else {
        console.error('Invalid categoryId format:', categoryId);
        return res.status(400).json({ error: 'Invalid category ID format' });
      }

      // Validate subCategoryId
      if (!mongoose.Types.ObjectId.isValid(subCategoryId)) {
        console.error('Invalid subCategoryId format:', subCategoryId);
        return res.status(400).json({ error: 'Invalid subcategory ID format' });
      }
      subCategoryIdObj = new mongoose.Types.ObjectId(subCategoryId);

    } catch (error) {
      console.error('Error converting IDs to ObjectId:', error);
      return res.status(400).json({
        error: 'Invalid ID format',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    console.log('Using ObjectIds for query:');
    console.log('- userId:', userIdObj);
    console.log('- categoryId:', categoryIdObj);
    console.log('- subCategoryId:', subCategoryIdObj);

    // Get the user to find their ownerId
    const user = await User.findById(userIdObj);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    let ownerId = null;
    if (user.ownerId) {
      ownerId = user.ownerId;
      console.log('- ownerId:', ownerId);
    } else {
      console.log('- User does not have an associated owner, proceeding without ownerId');
    }

    // Check if user already has answers for this category and subcategory
    const existingUserInput = await UserInput.findOne({
      userId: userIdObj,
      categoryId: categoryIdObj,
      subCategoryId: subCategoryIdObj
    });

    if (existingUserInput) {
      // Update existing user input
      console.log('Updating existing user input');
      existingUserInput.answersBySection = answersBySection;
      // Also update ownerId if user has one and it was missing
      if (ownerId && !existingUserInput.ownerId) {
        existingUserInput.ownerId = ownerId;
      }
      await existingUserInput.save();
      return res.status(200).json(existingUserInput);
    }

    // Create new user input
    console.log('Creating new user input');
    const userInputData: any = {
      userId: userIdObj,
      categoryId: categoryIdObj,
      subCategoryId: subCategoryIdObj,
      answersBySection
    };

    // Add ownerId if available
    if (ownerId) {
      userInputData.ownerId = ownerId;
    }

    const userInput = new UserInput(userInputData);

    await userInput.save();
    res.status(201).json(userInput);
  } catch (error: unknown) {
    console.error('Error saving answers:', error);

    // Check for specific MongoDB errors
    if (error instanceof Error) {
      const errorMessage = error.message;

      // Check for common MongoDB errors
      if (errorMessage.includes('Cast to ObjectId failed')) {
        return res.status(400).json({
          error: 'Invalid ID format',
          details: errorMessage,
          message: 'One of the IDs provided is not in the correct format for MongoDB'
        });
      } else if (errorMessage.includes('validation failed')) {
        return res.status(400).json({
          error: 'Validation error',
          details: errorMessage,
          message: 'The data provided did not pass validation'
        });
      }

      res.status(500).json({
        error: 'Error saving answers',
        details: errorMessage,
        message: 'There was a problem saving your answers'
      });
    } else {
      res.status(500).json({
        error: 'An unknown error occurred',
        message: 'There was an unexpected problem saving your answers'
      });
    }
  }
};

