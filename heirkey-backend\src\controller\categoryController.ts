import { Request, Response } from 'express';
import mongoose from 'mongoose';
import Category from '../models/category';


export const category = async (req: Request, res: Response): Promise<void> => {
    try {
        const { name } = req.body;

        if (!name) {
            res.status(400).json({ message: 'Category name is required.' });
            return;
        }

        const category = new Category({ name });
        await category.save();
        res.status(201).json(category);
    } catch (error) {
        res.status(500).json({ message: 'Error creating category', error });
    }
};

export const getAllCategories = async (req: Request, res: Response): Promise<void> => {
    try {
        const categories = await Category.find()
        res.status(200).json(categories);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching categories', error });
    }
};



export const getCategories = async (req: Request, res: Response): Promise<void> => {
    try {
        const categories = await Category.aggregate([
            {
                $lookup: {
                    from: 'subcategories',
                    localField: '_id',
                    foreignField: 'categoryId',
                    as: 'subcategories'
                }
            }
        ]);
        res.status(200).json(categories);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching categories', error });
    }
};

export const getFullCategoryDataQuestions = async (req: Request, res: Response): Promise<void> => {
    try {
        const { categoryId } = req.query;

        if (!categoryId || typeof categoryId !== 'string') {
            res.status(400).json({ message: 'Valid categoryId is required' });
            return;
        }

        console.log(`Received request for category with ID: ${categoryId}`);

        // Try multiple strategies to find the category
        let category;
        let query;

        try {
            // Strategy 1: If categoryId is a valid ObjectId, use it directly
            if (mongoose.Types.ObjectId.isValid(categoryId)) {
                console.log(`Trying to find category by ObjectId: ${categoryId}`);
                category = await Category.findById(categoryId);
                if (category) {
                    console.log(`Found category by ObjectId: ${category.name}`);
                    query = { _id: new mongoose.Types.ObjectId(categoryId) };
                }
            }

            // Strategy 2: If not found by ObjectId, try to find by name
            if (!category) {
                console.log(`Trying to find category by name containing: ${categoryId}`);
                category = await Category.findOne({
                    name: new RegExp(categoryId, 'i')
                });
                if (category) {
                    console.log(`Found category by name: ${category.name}`);
                    query = { _id: category._id };
                }
            }

            // Strategy 3: If numeric ID, try to find by numericId field
            if (!category && /^\d+$/.test(categoryId)) {
                console.log(`Trying to find category by numericId: ${categoryId}`);
                const numericId = parseInt(categoryId, 10);
                category = await Category.findOne({ numericId });
                if (category) {
                    console.log(`Found category by numericId: ${category.name}`);
                    query = { _id: category._id };
                } else {
                    // Fallback: Try to find by index position
                    console.log(`Trying to find category by index position: ${categoryId}`);
                    const allCategories = await Category.find().sort({ createdAt: 1 });
                    if (numericId > 0 && numericId <= allCategories.length) {
                        category = allCategories[numericId - 1];
                        if (category) {
                            console.log(`Found category by index: ${category.name}`);
                            query = { _id: category._id };
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error finding category:', error);
        }

        if (!category) {
            console.log(`Category not found for ID: ${categoryId}`);
            res.status(404).json({ message: 'Category not found' });
            return;
        }

        const result = await Category.aggregate([
            {
                $match: query || {} // Provide empty object as fallback
            },
            {
                $lookup: {
                    from: 'subcategories',
                    let: { categoryId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$categoryId', '$$categoryId'] }
                            }
                        },
                        {
                            $lookup: {
                                from: 'questions',
                                let: { subCategoryId: '$_id' },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: { $eq: ['$subCategoryId', '$$subCategoryId'] }
                                        }
                                    }
                                ],
                                as: 'questions'
                            }
                        }
                    ],
                    as: 'subcategories'
                }
            }
        ]);

        if (result.length === 0) {
            res.status(404).json({ message: 'Category data not found' });
            return;
        }

        res.status(200).json(result[0]);
    } catch (error) {
        console.error('Error in getCategoriesWithSubcategoriesAndQuestions:', error);
        res.status(500).json({
            message: 'Error fetching categories with subcategories and questions',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

// Utility endpoint to ensure categories exist
export const ensureDefaultCategories = async (req: Request, res: Response): Promise<void> => {
    try {
        // Define default categories
        const defaultCategories = [
            { name: 'Home Instructions', numericId: 1 },
            { name: 'Home Documents', numericId: 2 },
            { name: 'Will Location', numericId: 3 },
            { name: 'Funeral Arrangements', numericId: 4 },
            { name: 'Important Contacts', numericId: 5 },
            { name: 'Social Media and Phone', numericId: 6 },
        ];

        // Check if categories already exist
        const existingCategories = await Category.find();

        if (existingCategories.length === 0) {
            // Create categories if none exist
            const createdCategories = await Category.insertMany(defaultCategories);
            console.log(`Created ${createdCategories.length} default categories`);

            // Create default subcategories for Home Instructions
            const homeInstructionsCategory = createdCategories.find(c => c.numericId === 1);
            if (homeInstructionsCategory) {
                const subcategories = [
                    { name: 'Pets', description: 'Information about your pets', categoryId: homeInstructionsCategory._id },
                    { name: 'Trash', description: 'Trash pickup information', categoryId: homeInstructionsCategory._id },
                    { name: 'Other', description: 'Other home instructions', categoryId: homeInstructionsCategory._id },
                    { name: 'Security', description: 'Home security information', categoryId: homeInstructionsCategory._id },
                ];

                const Subcategory = mongoose.model('Subcategory');
                await Subcategory.insertMany(subcategories);
                console.log(`Created default subcategories for Home Instructions`);
            }

            res.status(201).json({
                message: 'Default categories created successfully',
                categories: createdCategories
            });
        } else {
            // Categories already exist
            res.status(200).json({
                message: 'Categories already exist',
                categories: existingCategories
            });
        }
    } catch (error) {
        console.error('Error ensuring default categories:', error);
        res.status(500).json({
            message: 'Error ensuring default categories',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

export { Category };
