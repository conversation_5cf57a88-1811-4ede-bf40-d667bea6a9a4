import api from "./api";
import { InviteForm, InviteResponse } from "@/types/invite.types";

// Send invite
export const sendInvite = async (data: InviteForm): Promise<InviteResponse> => {
  const res = await api.post("/v1/api/invitations/invite", data);
  return res.data as InviteResponse;
};

// Get sent invites
export const getSentInvites = async () => {
  const res = await api.get("/v1/api/invitations/sent");
  return res.data as InviteResponse[];
};

// Accept invitation
export const acceptInvite = async (token: string, password: string) => {
  const res = await api.post("/v1/api/invitations/accept", { token, password });
  return res.data;
}; 