import express from 'express';
import { inviteUser, getInvitations, acceptInvitation } from '../controller/invitationController';
import { inviteUserValidation, acceptInvitationValidation } from '../validation/invitationValidation';
import { combinedAuth } from '../middleware/authMiddleware';
import sendEmail from '../utils/email';

const router = express.Router();

// Test email endpoint
// router.post('/test-email', async (req, res) => {
//     try {
//         const { to } = req.body;

//         await sendEmail({
//             to: to || '<EMAIL>',
//             subject: 'Test Email from <PERSON><PERSON><PERSON>',
//             html: '<h1>Test Email</h1><p>If you receive this, email configuration is working!</p>',
//             text: 'Test Email - If you receive this, email configuration is working!'
//         });

//         res.status(200).json({ message: 'Test email sent successfully!' });
//     } catch (error) {
//         console.error('Test email error:', error);
//         res.status(500).json({
//             message: 'Failed to send test email',
//             error: error instanceof Error ? error.message : 'Unknown error'
//         });
//     }
// });

// Protected routes - Owner can invite users
router.post('/invite', combinedAuth, inviteUserValidation, inviteUser);
router.get('/sent', combinedAuth, getInvitations);

// Public route - Accept invitation (no auth required as user doesn't have account yet)
router.post('/accept', acceptInvitationValidation, acceptInvitation);

export default router;
