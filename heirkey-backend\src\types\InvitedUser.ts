import mongoose from 'mongoose';

export enum InvitationStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected'
}

export interface IInvitedUser extends mongoose.Document {
  _id: mongoose.Types.ObjectId;
  ownerId: mongoose.Types.ObjectId; // Reference to Owner model
  invitedUserId: mongoose.Types.ObjectId; // Reference to User model
  status: InvitationStatus;
  relation: string; // Relationship between owner and invited user
  invitationToken?: string; // Token for email verification
  invitationTokenExpire?: Date; // Token expiry
  createdAt: Date;
  updatedAt: Date;

  // Virtual fields (populated)
  owner?: any; // Populated Owner data
  invitedUser?: any; // Populated User data

  // Instance methods
  generateInvitationToken(): string;
}

export interface IInvitedUserModel extends mongoose.Model<IInvitedUser> {
  // Static methods
  findByOwnerId(ownerId: mongoose.Types.ObjectId): Promise<IInvitedUser[]>;
  findByUserId(userId: mongoose.Types.ObjectId): Promise<IInvitedUser[]>;
  findPendingInvitations(ownerId?: mongoose.Types.ObjectId): Promise<IInvitedUser[]>;
}

export interface ICreateInvitedUserData {
  ownerId: mongoose.Types.ObjectId;
  invitedUserId: mongoose.Types.ObjectId;
  relation: string;
  status?: InvitationStatus;
  invitationToken?: string;
  invitationTokenExpire?: Date;
}

export interface IInviteUserData {
  name: string;
  email: string;
  relation: string;
  phone?: string;
}

export interface IUpdateInvitedUserData {
  status?: InvitationStatus;
}
