import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';
import AuthHeader from '../header/gradiantHeader';
import authService from '@/services/authService';

const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

export default function ForgetPassword() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    mode: 'onChange',
  });

  const onSubmit = async (data: ForgotPasswordFormValues) => {
    setIsLoading(true);
    setError('');
    setMessage('');

    try {
      const response = await authService.forgotPassword(data.email);
      setMessage(response.message);

      // Show success message for a few seconds, then navigate
      setTimeout(() => {
        navigate('/auth/login', {
          state: {
            message: 'Password reset link sent! Check your email and follow the instructions.'
          }
        });
      }, 3000);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to send reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <AuthHeader title="Forgot Password" />
      <div className="bg-white flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md md:max-w-xl lg:max-w-2xl mx-auto">

        <Card className="w-full mt-6 shadow-md">
          <CardHeader>
            <div className="text-center">
              <h2 className="text-xl font-semibold text-[#1F2668]">
                Forgot your password?
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                Enter your email and we'll send you a reset link.
              </p>
            </div>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Success Message */}
              {message && (
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-sm text-green-700">{message}</p>
                  <p className="text-xs text-green-600 mt-1">Redirecting to login page...</p>
                </div>
              )}

              {/* Error Message */}
              {error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              )}

              <div>
                <Input
                  type="email"
                  placeholder="Enter your email"
                  {...register('email')}
                  className={`w-full p-3 text-base ${errors.email ? 'border-red-500' : ''}`}
                  disabled={isLoading}
                />
                {errors.email && (
                  <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full bg-[#2BCFD5] text-white py-3 text-base disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={!isValid || isLoading}
              >
                {isLoading ? 'Sending...' : 'Send Reset Link'}
              </Button>
            </form>
          </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}
