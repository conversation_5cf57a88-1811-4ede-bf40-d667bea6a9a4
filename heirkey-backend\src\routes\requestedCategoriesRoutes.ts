import express from 'express';
import { 
    requestCategories, 
    getMyRequests, 
    getOwnerRequests, 
    approveRequest 
} from '../controller/requestedCategoriesController';
import { 
    requestCategoriesValidation, 
    approveRequestValidation 
} from '../validation/requestedCategoriesValidation';
import { combinedAuth } from '../middleware/authMiddleware';

const router = express.Router();

// Protected routes - Nominee/Family can request categories
router.post('/request', combinedAuth, requestCategoriesValidation, requestCategories);
router.get('/my-requests', combinedAuth, getMyRequests);

// Protected routes - Owner can view requests
router.get('/owner-requests', combinedAuth, getOwnerRequests);

// Public route - Approve/reject request (no auth required as it uses token)
router.post('/approve', approveRequestValidation, approveRequest);

export default router;
