import React, { useEffect } from 'react';
import { Field, useFormikContext } from 'formik';
import { useAppDispatch } from '@/store/hooks';
import { saveUserInput, updateUserInput } from '@/store/slices/importantContactsSlice';
import { useAuth } from '@/contexts/AuthContext';

export interface ContactQuestion {
  id: string;
  text: string;
  type: 'text' | 'email' | 'phone' | 'choice';
  required: boolean;
  sectionId: string;
  options?: string[];
  validationRules?: {
    maxLength?: number;
    pattern?: string;
  };
  order: number;
  dependsOn?: {
    questionId: string;
    value: string;
  };
}

export const buildValidationSchema = (questions: ContactQuestion[], Yup: any) => {
  const schema: Record<string, any> = {};

  questions.forEach(question => {
    let fieldSchema;

    switch (question.type) {
      case 'text':
        fieldSchema = Yup.string();
        break;
      case 'email':
        fieldSchema = Yup.string().email('Invalid email address');
        break;
      case 'phone':
        fieldSchema = Yup.string().matches(
          /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/,
          'Invalid phone number'
        );
        break;
      case 'choice':
        fieldSchema = Yup.string();
        break;
      default:
        fieldSchema = Yup.string();
    }

    if (question.required) {
      fieldSchema = fieldSchema.required('This field is required');
    }

    if (question.type === 'text' && question.validationRules?.maxLength) {
      fieldSchema = fieldSchema.max(
        question.validationRules.maxLength,
        `Maximum ${question.validationRules.maxLength} characters allowed`
      );
    }

    schema[question.id] = fieldSchema;
  });

  return Yup.object().shape(schema);
};

export const generateInitialValues = (questions: ContactQuestion[]) => {
  const values: Record<string, any> = {};
  questions.forEach(question => {
    values[question.id] = '';
  });
  return values;
};

export const calculateProgress = (questions: ContactQuestion[], values: Record<string, any>) => {
  const totalQuestions = questions.length;
  const answeredQuestions = Object.values(values).filter(value => value !== '').length;
  const completionPercentage = Math.round((answeredQuestions / totalQuestions) * 100);

  return {
    totalQuestions,
    answeredQuestions,
    completionPercentage
  };
};

export const handleDependentAnswers = (
  values: Record<string, any>,
  questions: ContactQuestion[],
  setValues: (values: Record<string, any>) => void
) => {
  let needsUpdate = false;
  const newValues = { ...values };

  questions.forEach(question => {
    if (question.dependsOn) {
      const parentValue = values[question.dependsOn.questionId];
      const shouldClear = parentValue !== question.dependsOn.value && values[question.id] !== '';

      if (shouldClear) {
        newValues[question.id] = '';
        needsUpdate = true;
      }
    }
  });

  if (needsUpdate) {
    setValues(newValues);
  }
};

interface ContactQuestionItemProps {
  question: ContactQuestion;
  values: Record<string, any>;
  existingInputId?: string | null;
  onSave?: () => void;
}

export const ContactQuestionItem: React.FC<ContactQuestionItemProps> = ({ 
  question, 
  values, 
  existingInputId,
  onSave 
}) => {
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const { setFieldValue } = useFormikContext();

  const shouldShow = !question.dependsOn ||
    (values[question.dependsOn.questionId]?.toString().toLowerCase() === question.dependsOn.value.toLowerCase());

  const handleFieldChange = async (e: React.ChangeEvent<any>) => {
    const { name, value } = e.target;
    setFieldValue(name, value);

    if (user?.id) {
      try {
        const answer = {
          index: question.order,
          originalQuestionId: question.id,
          question: question.text,
          type: question.type,
          answer: value
        };

        const formattedAnswersBySection = [{
          originalSectionId: question.sectionId,
          isCompleted: true,
          answers: [answer]
        }];

        const userData = {
          userId: user.id,
          categoryId: '5',
          originalCategoryId: '207',
          subCategoryId: question.sectionId,
          originalSubCategoryId: question.sectionId,
          answersBySection: formattedAnswersBySection
        };

        if (existingInputId) {
          await dispatch(updateUserInput({ id: existingInputId, userData })).unwrap();
        } else {
          await dispatch(saveUserInput(userData)).unwrap();
        }

        if (onSave) {
          onSave();
        }
      } catch (error) {
        console.error('Error saving answer:', error);
      }
    }
  };

  if (!shouldShow) {
    return null;
  }

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {question.text}
        {question.required && <span className="text-red-500 ml-1">*</span>}
      </label>

      {question.type === 'text' && (
        <Field
          as="textarea"
          name={question.id}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
          rows={3}
          onChange={handleFieldChange}
        />
      )}

      {question.type === 'email' && (
        <Field
          type="email"
          name={question.id}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
          placeholder="Enter email address"
          onChange={handleFieldChange}
        />
      )}

      {question.type === 'phone' && (
        <Field
          type="tel"
          name={question.id}
          className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2BCFD5] focus:border-transparent"
          placeholder="Enter phone number"
          onChange={handleFieldChange}
        />
      )}

      {question.type === 'choice' && question.options && (
        <div className="space-y-2">
          {question.options.map(option => (
            <label key={option} className="flex items-center space-x-2">
              <Field
                type="radio"
                name={question.id}
                value={option}
                className="h-4 w-4 text-[#2BCFD5]"
                onChange={handleFieldChange}
              />
              <span className="text-sm text-gray-700">{option}</span>
            </label>
          ))}
        </div>
      )}
    </div>
  );
}; 