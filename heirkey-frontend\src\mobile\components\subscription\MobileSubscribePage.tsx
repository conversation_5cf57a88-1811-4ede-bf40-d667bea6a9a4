import { Button } from "@/components/ui/button";
import {
  ToggleGroup,
  ToggleGroupItem,
} from "@/components/ui/toggle-group";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { fetchPricingPlans, subscribeToPlan } from "@/store/slices/subscriptionSlice";
import subscriptionService from '@/services/subscriptionService';
import { useAuth } from '@/contexts/AuthContext';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import GradiantHeader from '../header/gradiantHeader';
import logoTwo from '@/assets/mobileimage/logo/logoTwo.png';

export default function MobileSubscribePage() {
  const [billing, setBilling] = useState("monthly");
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  
  const { plans, loading, error } = useSelector((state: RootState) => state.subscription);
  const { user } = useAuth();
  const [ownerId, setOwnerId] = useState<string | null>(null);
  const [ownerLoading, setOwnerLoading] = useState(true);
  const [checkingSubscription, setCheckingSubscription] = useState(false);

  // Map backend plans to include billingCycle
  const mappedPlans = plans.map(plan => ({
    ...plan,
    billingCycle:
      plan.duration === 1 ? "monthly" :
      plan.duration === 12 ? "annual" :
      "other"
  }));

  // Filter plans based on billing cycle
  const filteredPlans = mappedPlans.filter(plan => plan.billingCycle === billing);

  useEffect(() => {
    dispatch(fetchPricingPlans());
  }, [dispatch]);

  useEffect(() => {
    const fetchOwnerId = async () => {
      if (user) {
        try {
          setCheckingSubscription(true);
          
          // Check if user already has a subscription
          if (user.ownerId) {
            const existingSubscription = await subscriptionService.getOwnerSubscription(user.ownerId);
            if (existingSubscription) {
              console.log('User already has subscription, redirecting to dashboard');
              navigate('/dashboard');
              return;
            }
          }

          const cachedOwnerId = await getCachedOwnerIdFromUser(user);
          setOwnerId(cachedOwnerId);
        } catch (error) {
          console.error('Error fetching owner ID or checking subscription:', error);
          const cachedOwnerId = await getCachedOwnerIdFromUser(user);
          setOwnerId(cachedOwnerId);
        } finally {
          setOwnerLoading(false);
          setCheckingSubscription(false);
        }
      }
    };

    fetchOwnerId();
  }, [user, navigate]);

  const handleSelectPlan = async (planId: string) => {
    if (!ownerId) {
      console.error('Owner ID is required to subscribe.');
      return;
    }
    try {
      await dispatch(subscribeToPlan({ planId, ownerId })).unwrap();
      navigate("/dashboard");
    } catch (err) {
      console.error("Failed to subscribe:", err);
    }
  };

  if (loading || checkingSubscription) return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center py-10">Loading plans...</div>
    </div>
  );
  
  if (error) return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center text-red-500 py-10">{error}</div>
    </div>
  );

  return (
    <div className="min-h-screen bg-white">
      <GradiantHeader title="Choose Your Plan" logo={logoTwo} />
      
      <div className="px-4 py-6">
        {/* Header Section */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-[#1F4168] mb-2">Subscription Plans</h1>
          <p className="text-gray-600 mb-6">We have plans to fit your family's needs.</p>
          
          {/* Billing Toggle */}
          <ToggleGroup
            type="single"
            value={billing}
            onValueChange={(value) => value && setBilling(value)}
            className="bg-gray-100 rounded-lg p-1 w-fit mx-auto"
          >
            <ToggleGroupItem 
              value="monthly" 
              className="px-6 py-2 rounded-md data-[state=on]:bg-white data-[state=on]:text-[#1F4168] data-[state=on]:shadow-sm"
            >
              Monthly
            </ToggleGroupItem>
            <ToggleGroupItem 
              value="annual" 
              className="px-6 py-2 rounded-md data-[state=on]:bg-white data-[state=on]:text-[#1F4168] data-[state=on]:shadow-sm"
            >
              Annual
            </ToggleGroupItem>
          </ToggleGroup>
        </div>

        {/* Plans Grid */}
        <div className="space-y-4 max-w-md mx-auto">
          {filteredPlans.map((plan) => (
            <Card
              key={plan._id}
              className="border-2 border-gray-200 rounded-xl overflow-hidden"
            >
              <CardHeader className="bg-[#F0F9FF] text-center py-6">
                <div className="mb-3">
                  <span className="inline-flex items-center gap-2 px-3 py-1 rounded-full border border-[#D1E9F7] bg-white text-[#1F4168] text-sm font-semibold">
                    <span className="w-2 h-2 rounded-full bg-[#2BCFD5] inline-block"></span>
                    {plan.type.replace(/_/g, " ").replace(/\b\w/g, (l: string) => l.toUpperCase())}
                  </span>
                </div>
                <CardTitle className="text-3xl font-bold mb-2">{plan.displayPrice}</CardTitle>
                <CardDescription className="text-gray-600">{plan.tagline}</CardDescription>
              </CardHeader>
              
              <CardContent className="p-6">
                <ul className="space-y-3 text-sm text-gray-700 mb-6">
                  {plan.features.map((feature: string, i: number) => (
                    <li key={i} className="flex items-start gap-3">
                      <span className="text-[#2BCFD5] mt-0.5 flex-shrink-0">✓</span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button
                  className="w-full font-semibold rounded-lg py-3 bg-[#2BCFD5] hover:bg-[#1F4168] text-white transition-colors"
                  onClick={() => handleSelectPlan(plan._id)}
                  disabled={ownerLoading || !ownerId}
                >
                  Get started
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>Need help choosing? <span className="text-[#2BCFD5] font-medium">Contact support</span></p>
        </div>
      </div>
    </div>
  );
}
