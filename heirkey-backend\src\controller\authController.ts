import { NextFunction, Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import User from '../models/User';
import Owner from '../models/Owner';
import Role from '../models/Role';
import { AuthRequest } from '../middleware/authMiddleware';
import crypto from 'crypto';
import { createToken } from '../utils/jwtUtils';
import { IUser } from '../types/User';
import { RoleType } from '../types/Role';
import sendEmail, { sendEmailVerificationOTP, sendPasswordResetEmail, sendPasswordResetSuccessEmail } from '../utils/email';
import { CustomError } from '../utils/customError';
import mongoose from 'mongoose';


export const registerUser = async (req: Request, res: Response): Promise<void> => {
    try {
        const { username, email, password, firstName, lastName } = req.body;

        // Check if user already exists
        const existingUser = await User.findOne({ email });
        if (existingUser) {
            res.status(400).json({ message: 'User already exists' });
            return;
        }

        // Check if owner already exists with this email
        const existingOwner = await Owner.findOne({ email: email.toLowerCase() });
        if (existingOwner) {
            res.status(400).json({ message: 'Owner with this email already exists' });
            return;
        }

        // Find the Owner role
        const ownerRole = await Role.findOne({ name: RoleType.OWNER });
        if (!ownerRole) {
            res.status(500).json({ message: 'Owner role not found. Please initialize default roles first.' });
            return;
        }

        // Create User record first (email not verified initially)
        const user = new User({
            username,
            email,
            password,
            firstName,
            lastName,
            roleId: ownerRole._id,
            isEmailVerified: false
        });

        await user.save();

        // Create Owner record
        const owner = new Owner({
            userId: user._id,
            username,
            email: email.toLowerCase(),
            firstName,
            lastName,
            externalUser: false
        });

        await owner.save();

        // Update User with Owner ID
        user.ownerId = owner._id;
        await user.save();

        // Generate email verification OTP
        const otp = user.generateEmailVerificationToken();
        await user.save({ validateBeforeSave: false });

        // Debug: Log OTP in development mode
        // if (process.env.NODE_ENV === 'development') {
        //     console.log(`🔐 DEBUG: Email verification OTP for ${user.email}: ${otp}`);
        // }

        // Send verification email
        try {
            await sendEmailVerificationOTP(user.email, user.username || user.firstName || 'User', otp);

            res.status(201).json({
                message: 'Registration successful! Please check your email for verification code.',
                email: user.email,
                requiresVerification: true
            });
        } catch (emailError) {
            console.error('Failed to send verification email:', emailError);
            // If email fails, still allow registration but inform user
            res.status(201).json({
                message: 'Registration successful! However, we could not send the verification email. Please try to resend it.',
                email: user.email,
                requiresVerification: true,
                emailError: true
            });
        }
    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            message: 'Error registering user',
            error: error instanceof Error ? error.message : 'Unknown error',
        });
    }
};

// Email verification endpoint
export const verifyEmail = async (req: Request, res: Response): Promise<void> => {
    try {
        const { email, otp } = req.body;

        if (!email || !otp) {
            res.status(400).json({ message: 'Email and OTP are required' });
            return;
        }

        // Hash the provided OTP to compare with stored hash
        const hashedOtp = crypto.createHash('sha256').update(otp).digest('hex');

        const user = await User.findOne({
            email,
            emailVerificationToken: hashedOtp,
            emailVerificationExpire: { $gt: Date.now() }
        });

        if (!user) {
            res.status(400).json({ message: 'Invalid or expired verification code' });
            return;
        }

        // Mark email as verified and clear verification fields
        user.isEmailVerified = true;
        user.emailVerificationToken = undefined;
        user.emailVerificationExpire = undefined;
        await user.save();

        // Generate JWT token for auto-login
        const token = createToken(user._id.toString(), process.env.JWT_SECRET as string);

        // Populate roleId to get role name
        await user.populate('roleId');

        res.status(200).json({
            message: 'Email verified successfully! Logging you in...',
            verified: true,
            autoLogin: true,
            token,
            user: {
                id: user._id,
                username: user.username,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                roleId: user.roleId?._id,
                role: typeof user.roleId === 'object' && user.roleId !== null && 'name' in user.roleId ? (user.roleId as any).name : undefined,
                ownerId: user.ownerId
            }
        });
    } catch (error) {
        // console.error('Email verification error:', error);
        res.status(500).json({
            message: 'Error verifying email',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

// Resend verification OTP
export const resendVerificationOTP = async (req: Request, res: Response): Promise<void> => {
    try {
        const { email } = req.body;

        if (!email) {
            res.status(400).json({ message: 'Email is required' });
            return;
        }

        const user = await User.findOne({ email });
        if (!user) {
            res.status(404).json({ message: 'User not found' });
            return;
        }

        if (user.isEmailVerified) {
            res.status(400).json({ message: 'Email is already verified' });
            return;
        }

        // Generate new OTP
        const otp = user.generateEmailVerificationToken();
        await user.save({ validateBeforeSave: false });

        // Debug: Log OTP in development mode
        if (process.env.NODE_ENV === 'development') {
            console.log(`🔐 DEBUG: Resend verification OTP for ${user.email}: ${otp}`);
        }

        // Send verification email
        try {
            await sendEmailVerificationOTP(user.email, user.username || user.firstName || 'User', otp);

            res.status(200).json({
                message: 'Verification code sent successfully!',
                email: user.email
            });
        } catch (emailError) {
            console.error('Failed to send verification email:', emailError);
            res.status(500).json({
                message: 'Failed to send verification email. Please try again.',
                error: 'Email service error'
            });
        }
    } catch (error) {
        console.error('Resend verification error:', error);
        res.status(500).json({
            message: 'Error resending verification code',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

export const loginUser = async (req: Request, res: Response): Promise<void> => {
    try {
        const { email, password } = req.body;

        const user = await User.findOne({ email }).populate('roleId') as IUser;
        if (!user) {
            console.log('User not found');
            res.status(400).json({ message: 'Invalid credentials' });
            return;
        }

        // Check if user is an external user (Google, etc.)
        if (user.externalUser) {
            res.status(400).json({ message: 'This account was created with Google. Please use Google Sign-In.' });
            return;
        }

        // Check if user has a password set
        if (!user.password) {
            res.status(400).json({ message: 'This account does not have a password set. Please use Google Sign-In.' });
            return;
        }

        // Check if email is verified
        if (!user.isEmailVerified) {
            res.status(400).json({
                message: 'Please verify your email before logging in',
                requiresVerification: true,
                email: user.email
            });
            return;
        }

        const isMatch = await user.comparePassword(password);

        if (!isMatch) {
            res.status(400).json({ message: 'Invalid credentials' });
            return;
        }

        // Generate JWT token
        const token = createToken(user._id.toString()!, process.env.JWT_SECRET as string);

        res.json({
            message: 'Login successful',
            token,
            user: {
                id: user._id,
                username: user.username,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                roleId: user.roleId?._id,
                role: typeof user.roleId === 'object' && user.roleId !== null && 'name' in user.roleId ? (user.roleId as any).name : undefined,
                ownerId: user.ownerId
            }
        });
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            message: 'Error logging in',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

export const getUserProfile = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?._id;
        const user = await User.findById(userId).select('-password').populate('roleId');

        if (!user) {
            res.status(404).json({ message: 'User not found' });
            return;
        }

        res.json({
            id: user._id,
            username: user.username,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            image: user.image,
            phone: user.phone,
            address: user.address,
            zipCode: user.zipCode,
            country: user.country,
            roleId: user.roleId?._id,
            role: typeof user.roleId === 'object' && user.roleId !== null && 'name' in user.roleId ? (user.roleId as any).name : undefined,
            ownerId: user.ownerId
        });
    } catch (error) {
        console.error('Error fetching profile:', error);
        res.status(500).json({
            message: 'Error fetching profile',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

export const updateUserProfile = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const userId = req.user?._id;
        const { firstName, lastName, username, phone, address, zipCode, country } = req.body;

        const user = await User.findById(userId);
        if (!user) {
            res.status(404).json({ message: 'User not found' });
            return;
        }

        // Only update fields that are provided
        if (firstName !== undefined) user.firstName = firstName;
        if (lastName !== undefined) user.lastName = lastName;
        if (username !== undefined) user.username = username;
        if (phone !== undefined) user.phone = phone;
        if (address !== undefined) user.address = address;
        if (zipCode !== undefined) user.zipCode = zipCode;
        if (country !== undefined) user.country = country;

        await user.save();

        res.json({
            id: user._id,
            username: user.username,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            image: user.image,
            phone: user.phone,
            address: user.address,
            zipCode: user.zipCode,
            country: user.country
        });
    } catch (error) {
        console.error('Error updating profile:', error);
        res.status(500).json({
            message: 'Error updating profile',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

export const updateUserProfileImage = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        if (!req.file) {
            res.status(400).json({ message: 'No image file provided' });
            return;
        }

        const userId = req.user?._id;
        if (!userId) {
            res.status(401).json({ message: 'User not authenticated' });
            return;
        }

        const user = await User.findById(userId);
        if (!user) {
            res.status(404).json({ message: 'User not found' });
            return;
        }

        // Update user's profile image with just the filename
        user.image = req.file.filename;
        await user.save();

        res.status(200).json({
            message: 'Profile image updated successfully',
            image: user.image
        });
    } catch (error) {
        console.error('Error updating profile image:', error);
        res.status(500).json({ message: 'Error updating profile image' });
    }
};

export const forgetPassword = async (req: Request, res: Response, next: NextFunction) => {
    const { email } = req.body;
    let findUser: IUser | null = null;

    try {
      findUser = await User.findOne({ email });

      if (!findUser) {
        return next(new CustomError(`We could not find the email ${email} on the server`, 404));
      }

      const resetToken = findUser.generateResetToken(); // schema method
      await findUser.save({ validateBeforeSave: false });

      const resetUrl = `${process.env.FRONTEND_URL}/auth/resetpassword?token=${resetToken}`;
      const message = `We have received a password reset request for ${findUser.username || findUser.firstName || 'User'}. Please use the link below to reset your password:\n\n${resetUrl}\n\nThis link is valid for 10 minutes.`;

      const htmlMessage = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #1F2668;">Password Reset Request</h2>
          <p>Hello ${findUser.username || findUser.firstName || 'User'},</p>
          <p>We have received a password reset request for your Heirkey account.</p>
          <p>Please click the button below to reset your password:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" style="background-color: #2BCFD5; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Reset Password</a>
          </div>
          <p>Or copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${resetUrl}</p>
          <p><strong>This link is valid for 10 minutes only.</strong></p>
          <p>If you did not request this password reset, please ignore this email.</p>
          <p>Best regards,<br>The Heirkey Team</p>
        </div>
      `;

      await sendEmail({
        to: findUser.email,
        subject: "Password Reset Request - Heirkey",
        text: message,
        html: htmlMessage,
      });

      res.status(200).json({
        message: `Password reset token sent to your email ${findUser.email}`,
      });
    } catch (error: any) {
      if (findUser) {
        findUser.resetPasswordToken = undefined;
        findUser.resetPasswordExpire = undefined;
        await findUser.save({ validateBeforeSave: false });
      }
      return next(new CustomError(error.message, 500));
    }
  };


export const resetPassword = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { password } = req.body;

    // Validate password is provided
    if (!password) {
      return next(new CustomError("Password is required", 400));
    }

    // Validate password strength (same as registration)
    if (password.length < 8) {
      return next(new CustomError("Password must be at least 8 characters long", 400));
    }

    if (!/[^A-Za-z0-9]/.test(password)) {
      return next(new CustomError("Password must contain at least one special character", 400));
    }

    const hashedToken = crypto.createHash("sha256").update(req.params.token).digest("hex");

    const user = await User.findOne({
      resetPasswordToken: hashedToken,
      resetPasswordExpire: { $gt: Date.now() },
    });

    if (!user) {
      return next(new CustomError("Token is invalid or has expired", 400));
    }

    // Update the password
    user.password = password; // This will be hashed by the pre-save middleware
    user.resetPasswordToken = undefined;
    user.resetPasswordExpire = undefined;
    await user.save();

    // Send confirmation email
    try {
      await sendEmail({
        to: user.email,
        subject: "Password Reset Successful - Heirkey",
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #1F2668;">Password Reset Successful</h2>
            <p>Hello ${user.username || user.firstName || 'User'},</p>
            <p>Your password has been successfully reset for your Heirkey account.</p>
            <p>If you did not make this change, please contact our support team immediately.</p>
            <p>Best regards,<br>The Heirkey Team</p>
          </div>
        `,
        text: `Hello ${user.username || user.firstName || 'User'}, Your password has been successfully reset for your Heirkey account. If you did not make this change, please contact our support team immediately.`
      });
    } catch (emailError) {
      console.error('Failed to send password reset confirmation email:', emailError);
      // Don't fail the request if email fails
    }

    const token = createToken(user._id.toString(), process.env.JWT_SECRET as string);

    res
      .cookie("token", token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        maxAge: 30 * 24 * 60 * 60 * 1000,
      })
      .status(200)
      .json({
        message: "Your password has been successfully reset",
        data: {
          id: user._id,
          email: user.email,
          username: user.username
        }
      });
  } catch (error) {
    console.error('Reset password error:', error);
    return next(new CustomError("Something went wrong while resetting password", 500));
  }
};


export const getAllUsers = async (_req: Request, res: Response): Promise<void> => {
    try {
        const users = await User.find().select('-password');
        console.log(users);
        res.json(users);
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({
            message: 'Error fetching users',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

export const userDetails = async (req: Request, res: Response) => {
  try {
    const { username, email, firstName, lastName, password } = req.body;
    const image = req.file?.filename;

    // Hash the password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create or update user logic here
    const user = new User({
      username,
      email,
      firstName,
      lastName,
      password: hashedPassword, // Store hashed password
      image: image
    });

    await user.save();

    res.json(user);
  } catch (error) {
    console.error('Error updating user details:', error);
    res.status(500).json({
      message: 'Error updating user details',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};


export const getProfile = (req: Request, res: Response): void => {
  if (!req.user) {
    res.status(401).json({ message: 'Not authenticated' });
    return;
  }
  res.json(req.user);
};


// export const logout = (req: Request, res: Response) => {

//   req.logout(err => {
//     if (err) return res.status(500).json({ message: 'Logout error' });
//     req.session.destroy(() => {
//       res.clearCookie('connect.sid');
//       res.json({ message: 'Logged out successfully' });
//     });
//   });
// };

export const logout = async (req: Request & { user?: { userId: string } }, res: Response) => {
  try {
    res.clearCookie('token');
    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    console.error('Error during logout:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Debug endpoint to get user verification status (development only)
export const getVerificationStatus = async (req: Request, res: Response): Promise<void> => {
    try {
        const { email } = req.params;

        const user = await User.findOne({ email }).select('email isEmailVerified emailVerificationToken emailVerificationExpire');

        if (!user) {
            res.status(404).json({ message: 'User not found' });
            return;
        }

        // Only show this in development
        if (process.env.NODE_ENV === 'development') {
            res.status(200).json({
                email: user.email,
                isEmailVerified: user.isEmailVerified,
                hasVerificationToken: !!user.emailVerificationToken,
                tokenExpired: user.emailVerificationExpire ? user.emailVerificationExpire < new Date() : true
            });
        } else {
            res.status(403).json({ message: 'Not available in production' });
        }
    } catch (error) {
        console.error('Get verification status error:', error);
        res.status(500).json({
            message: 'Error getting verification status',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

