import { useAuth } from '@/contexts/AuthContext';
import GradiantHeader from '@/mobile/components/header/gradiantHeader';
import Footer from '@/mobile/components/layout/Footer';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  fetchUserInputs,
  selectError,
  selectLoading,
  selectSubcategories,
  selectUserInputs,
  SubCategory
} from '@/store/slices/homeInstructionsSlice';
import { CheckCircle } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

const SubCategoryCard = ({
  subcategory,
  isActive,
  onClick,
  completedQuestions
}: {
  subcategory: SubCategory;
  isActive: boolean;
  onClick: () => void;
  completedQuestions: number;
}) => {
  const navigate = useNavigate();
  // Check if all questions are completed
  const isCompleted = completedQuestions === subcategory.questionsCount && subcategory.questionsCount > 0;

  return (
    <div
      className={
        "cursor-pointer flex items-center justify-between px-4 py-3 rounded-xl border transition-all " +
        (isActive
          ? "border-[#2BCFD5] bg-white shadow"
          : "border-gray-200 bg-gray-50 hover:bg-white")
      }
      onClick={onClick}
    >
      <span className="font-medium text-gray-900">{subcategory.title}</span>
      <div className="flex items-center gap-2">
        <span className="text-xs text-gray-500">
          {completedQuestions}/{subcategory.questionsCount} questions
        </span>
        {isCompleted && (
          <>
            <CheckCircle className="w-5 h-5 text-[#2BCFD5]" />
            <button
              className="text-xs text-[#2BCFD5] font-semibold px-2 py-1 rounded hover:underline"
              onClick={e => {
                e.stopPropagation();
                navigate(`/category/homeinstructions/${subcategory.title.toLowerCase()}`);
              }}
            >
              Edit
            </button>
          </>
        )}
      </div>
    </div>
  );
};

const HomeInstructionsPage = () => {
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const subcategories = useAppSelector(selectSubcategories);
  const userInputs = useAppSelector(selectUserInputs);
  const loading = useAppSelector(selectLoading);
  const error = useAppSelector(selectError);
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<string | null>(null);

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (user?.id) {
        try {
          const ownerId = await getCachedOwnerIdFromUser(user);
          if (ownerId) {
            dispatch(fetchUserInputs(ownerId));
          } else {
            console.error('No owner ID found for user in HomeInstructionsPage component');
          }
        } catch (error) {
          console.error('Error fetching owner ID in HomeInstructionsPage component:', error);
        }
      }
    };

    fetchData();
  }, [dispatch, user]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <GradiantHeader title="Home Instructions" showAvatar={true} />
        <div className="container mx-auto px-4 py-6 text-center">
          Loading your home instructions...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <GradiantHeader title="Home Instructions" showAvatar={true} />
        <div className="container mx-auto px-4 py-6 text-center text-red-500">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <GradiantHeader 
        showAvatar={true}
        title="Home Instructions"
      />
      <div className="container mx-auto px-4 py-6 max-w-md">
        {/* Understanding Topics Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-5 mb-6">
          <h2 className="text-base font-bold text-[#8B5CF6] mb-2">Understanding Topics</h2>
          <p className="text-sm text-gray-600">
            Each topic below is a part of your home documents, with questions to help you provide important information for you and your loved ones. Click any topic to answer the questions at your own pace—we'll save everything for you.
          </p>
        </div>
        <div className="space-y-3">
          {(() => {
            const subcategoryPaths: Record<string, string> = {
              '105': 'homelocation',
              '101': 'pets',
              '102': 'trash',
              '104': 'security',
              '103': 'other',
            };
            return ['105', '101', '102', '104', '103']
              .map(id => subcategories.find(subcat => subcat.id === id))
              .filter(Boolean)
              .map(subcat => {
                const sub = subcat as SubCategory;
                const completedQuestions = userInputs.reduce((count, input) => {
                  if (input.originalSubCategoryId === sub.id) {
                    return count + input.answersBySection.reduce(
                      (sectionCount, section) => sectionCount + section.answers.length, 0
                    );
                  }
                  return count;
                }, 0);
                const isActive = activeTab === sub.id;
                return (
                  <SubCategoryCard
                    key={sub.id}
                    subcategory={sub}
                    isActive={isActive}
                    completedQuestions={completedQuestions}
                    onClick={() => {
                      setActiveTab(sub.id);
                      navigate(`/category/homeinstructions/${subcategoryPaths[sub.id]}`);
                    }}
                  />
                );
              });
          })()}
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default HomeInstructionsPage;