import express from 'express';
import {
  getAllOwners,
  getOwnerById,
  getOwnerByUserId,
  getMyOwnerProfile,
  updateOwner,
  updateMyOwnerProfile,
  deleteOwner,
  getOwnerByEmail
} from '../controller/ownerController';
import { validateUpdateOwner } from '../validation/ownerValidation';
import { combinedAuth } from '../middleware/authMiddleware';

const router = express.Router();

// Apply authentication to all routes
router.use(combinedAuth);

// Owner management routes
router.get('/', getAllOwners);                           // GET /owners - Get all owners
router.get('/profile', getMyOwnerProfile);               // GET /owners/profile - Get current user's owner profile
router.put('/profile', validateUpdateOwner, updateMyOwnerProfile); // PUT /owners/profile - Update current user's owner profile
router.get('/user/:userId', getOwnerByUserId);           // GET /owners/user/:userId - Get owner by user ID
router.get('/email/:email', getOwnerByEmail);            // GET /owners/email/:email - Get owner by email
router.get('/:id', getOwnerById);                        // GET /owners/:id - Get owner by ID
router.put('/:id', validateUpdateOwner, updateOwner);    // PUT /owners/:id - Update owner by ID
router.delete('/:id', deleteOwner);                      // DELETE /owners/:id - Delete owner by ID

export default router;
