name: HRKY-BACKEND-DEPLOYMENTS-BUILD-PUSH

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Which environment to deploy to?'
        required: true
        type: choice
        options:
          - prod
          - develop

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH and Deploy to EC2
        run: |
          echo "Deploying code to EC2 instance..."

          # Write the private key to a temporary file
          echo "${{ secrets.EC2_PRIVATE_KEY }}" | tr -d '\r' > private-key.pem
          chmod 400 private-key.pem

          # Capture the branch value in a variable
          BRANCH="${{ github.event.inputs.environment }}"

          # Pass secrets and branch inside SSH correctly
          ssh -v -o StrictHostKeyChecking=no -i private-key.pem ubuntu@${{ secrets.EC2_PUBLIC_IP }} "
            export MY_GITHUB_USERNAME='${{ secrets.MY_GITHUB_USERNAME }}';
            export MY_GITHUB_TOKEN='${{ secrets.MY_GITHUB_TOKEN }}';
            export BRANCH='${{ github.event.inputs.environment }}';
            bash -s
          " << EOF
            echo "Connected to EC2 instance, current directory: \$(pwd)"

            cd /home/<USER>/project/hierarchy/backend/heirkey-backend
      
            echo "Cleaning up any old git rebase conflicts..."
            if [ -d ".git/rebase-merge" ]; then
              rm -rf .git/rebase-merge
            fi

            echo "Clearing cached Git credentials..."
            git credential-cache exit

            echo "Checking out branch: \$BRANCH"
            git fetch origin
            git checkout \$BRANCH
            git reset --hard origin/\$BRANCH

            echo "Pulling latest code securely..."
            git pull "https://\$MY_GITHUB_USERNAME:\$<EMAIL>/sundar2892/heirkey-backend.git" \$BRANCH

            echo "Running npm install..."
            npm install

            echo "Building project..."
            npm run build

            echo "Restarting app with PM2..."
            pm2 restart heirkey-backend
          EOF

          # Clean up private key
          rm private-key.pem

        shell: /usr/bin/bash -e {0}

      - name: Notify Slack on Success
        if: success()
        run: |
          curl -X POST -H 'Content-type: application/json' \
          --data '{"text":" *Backend Build & Deploy Succeeded!* Commit: ${{ github.sha }}"}' \
          ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Slack on Failure
        if: failure()
        run: |
          curl -X POST -H 'Content-type: application/json' \
          --data '{"text":" *Backend Build & Deploy Failed!* Commit: ${{ github.sha }}"}' \
          ${{ secrets.SLACK_WEBHOOK_URL }}
