declare module 'react-select-country-list' {
    interface CountryData {
      value: string;
      label: string;
    }
  
    interface CountryList {
      getData: () => CountryData[];
      getLabel: (value: string) => string | undefined;
      getValue: (label: string) => string | undefined;
      getValues: () => string[];
      getLabels: () => string[];
      getValueByLabel: (label: string) => string | undefined;
      getLabelByValue: (value: string) => string | undefined;
    }
  
    export default function countryList(): CountryList;
  }
  