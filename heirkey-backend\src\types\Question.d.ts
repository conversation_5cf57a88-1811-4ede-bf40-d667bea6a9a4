import { Types } from 'mongoose';

export interface IAnswer {
  index: number;
  questionId: Types.ObjectId;
  question: string;
  type: 'boolean' | 'text' | 'number' | 'choice' | 'date';
}

// Interface for AnswerBySection
export interface IAnswerBySection {
  isCompleted: boolean;
  answers: IAnswer[];
  sectionId: Types.ObjectId;
}

// Interface for Question Document
export interface IQuestion extends Document {
  subCategoryId: Types.ObjectId;
  categoryId: Types.ObjectId;
  answersBySection: IAnswerBySection[];
}