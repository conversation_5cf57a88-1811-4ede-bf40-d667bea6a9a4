import useMediaQuery from "use-media";
import { Routes, Route, BrowserRouter as Router, useParams } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import GoogleAuthCallback from "./components/auth/GoogleAuthCallback";
import SubscribePage from "@/components/subscription/SubscribePage";
import InviteDialog from "@/components/common/InviteDialog";
import AcceptInvitation from "@/components/common/AcceptInvitation";
import ApproveCategoryRequest from "@/components/common/ApproveCategoryRequest";


// Mobile imports
import Layout from "@/mobile/components/layout/Layout";
import LandingPage from "@/mobile/pages/LandingPage";
import LoginPage from "@/mobile/pages/AuthPages/LoginPage";
import SplashPage from "@/mobile/pages/Global/SplashPage";
import RegisterPage from "@/mobile/pages/AuthPages/RegisterPage";
import ResetPassword from "@/mobile/components/auth/ResetPassword";
import UserProfile from "@/mobile/components/auth/UserProfile";
import ForgetPassword from "@/mobile/components/auth/ForgetPassword";
import VerificationForm from "@/mobile/components/auth/EmailVerificationForm";
import EmailVerifyPage from "@/mobile/pages/AuthPages/EmailVerifyPage";
import DashboardPage from "@/mobile/pages/Dashboard/DashboardPage";

import CategoryConfirmPage from "@/mobile/pages/ConfirmationPage/CategoryConfirmPage";

import HomeInstructionsPage from '@/mobile/pages/Dashboard/HomeInstructions/HomeInstructionsPage';
import HomeLocationInstructionsPage from '@/mobile/pages/Dashboard/HomeInstructions/HomeLocationInstructionsPage';
import PetsInstructionsPage from '@/mobile/pages/Dashboard/HomeInstructions/PetsInstructionsPage';
import TrashInstructionsPage from "@/mobile/pages/Dashboard/HomeInstructions/TrashInstructionsPage";
import OtherInstructionsPage from "@/mobile/pages/Dashboard/HomeInstructions/OtherInstructionsPage";
import SecurityInstructionsPage from "@/mobile/pages/Dashboard/HomeInstructions/SecurityInstructionsPage";
import HomeInstructionsReviewPage from "@/mobile/pages/Dashboard/HomeInstructions/HomeInstructionsReviewPage";

import LocationInstrucationsPage from "@/mobile/pages/Dashboard/WillLocation/LocationInstrucationsPage";
import LegalInstructionsPage from "@/mobile/pages/Dashboard/WillLocation/LegalInstructionsPage";
import WillInstructionsPage from "@/mobile/pages/Dashboard/WillLocation/WillInstructionsPage";
import WillInstructionsReviewPage from "@/mobile/pages/Dashboard/WillLocation/WillInstructionReviewPage";

import FuneralArrangementsPage from "@/mobile/pages/Dashboard/FuneralArrangements/FuneralArrangementsPage";
import FuneralDetailsPage from "@/mobile/pages/Dashboard/FuneralArrangements/FuneralDetailsPage";
import CeremonyLocationPage from '@/mobile/pages/Dashboard/FuneralArrangements/CeremonyLocationPage';
import FuneralClergyPage from '@/mobile/pages/Dashboard/FuneralArrangements/FuneralClergyPage';
import FuneralNotificationPage from '@/mobile/pages/Dashboard/FuneralArrangements/FuneralNotificationPage';
import FuneralProceedingsPage from '@/mobile/pages/Dashboard/FuneralArrangements/FuneralProceedingsPage';
import FuneralArrangementReviewPage from "@/mobile/pages/Dashboard/FuneralArrangements/FuneralArrangementReviewPage";

import SocialMediaPhonePage from "@/mobile/pages/Dashboard/SocialMediaPhone/SocialMediaPhonePage";
import FacebookPage from "@/mobile/pages/Dashboard/SocialMediaPhone/FaceBookPage";
import InstagramPage from "@/mobile/pages/Dashboard/SocialMediaPhone/InstagramPage";
import EmailPage from "@/mobile/pages/Dashboard/SocialMediaPhone/EmailPage";
import OtherAccountsPage from "@/mobile/pages/Dashboard/SocialMediaPhone/OtherAccountsPage";
import CellPhonePage from "@/mobile/pages/Dashboard/SocialMediaPhone/CellPhonePage";
import SocialMediaPhoneReviewPage from "@/mobile/pages/Dashboard/SocialMediaPhone/SocialMediaPhoneReviewPage";

import ContactPage from "@/mobile/pages/Dashboard/ImportantContacts/ContactPage";
import FriendsandFamily from "@/mobile/pages/Dashboard/ImportantContacts/FriendsandFamily";
import WorkPage from "@/mobile/pages/Dashboard/ImportantContacts/WorkPage";
import ReligiousAffiliationPage from "@/mobile/pages/Dashboard/ImportantContacts/ReligiousAffiliation";
import ClubsPage from "@/mobile/pages/Dashboard/ImportantContacts/ClubsPage";
import ContactReviewPage from "@/mobile/pages/Dashboard/ImportantContacts/ContactReviewPage";

import HomeDocumentsPage from "@/mobile/pages/Dashboard/HomeDocuments/HomeDocumentsPage";
import UtilityPage from "@/mobile/pages/Dashboard/HomeDocuments/UtilityPage";
import GasPage from "@/mobile/pages/Dashboard/HomeDocuments/GasPage";
import WaterPage from "@/mobile/pages/Dashboard/HomeDocuments/WaterPage";
import TrashPage from "@/mobile/pages/Dashboard/HomeDocuments/TrashPage";
import HvacPage from "@/mobile/pages/Dashboard/HomeDocuments/HvacPage";
import PestPage from "@/mobile/pages/Dashboard/HomeDocuments/PestPage";
import LawnPage from "@/mobile/pages/Dashboard/HomeDocuments/LawnPage"; 
import CablePage from "@/mobile/pages/Dashboard/HomeDocuments/CablePage";
import InternetPage from "@/mobile/pages/Dashboard/HomeDocuments/InternetPage";
import HomeDocumentsReviewPage from "@/mobile/pages/Dashboard/HomeDocuments/HomeDocumentsReviewPage";

// Web imports
import WebLandingPage from "@/web/WebLandingPage";
import WebSplashPage from "@/web/pages/Global/WebSplashPage";
import WebAuthForm from "@/web/components/auth/WebAuthForm";
import WebVerificationForm from "@/web/components/auth/WebVerificationForm";
import WebResetPassword from "@/web/components/auth/WebResetPassword";
import WebForgetPassword from "@/web/components/auth/WebForgetPassword";
import WebUserProfile from "@/web/components/auth/WebUserProfile";
import WebLayout from "@/web/components/Layout/WebLayout";
import Dashboard from "@/web/pages/Dashboard/Dashboard";

import CategoryStartup from "./web/pages/Global/CategoryStartup";

import HomeLocationInstructions from "@/web/pages/Dashboard/HomeInstructions/HomeLocationInstructions";
import HomeInstructions from "@/web/pages/Dashboard/HomeInstructions/HomeInstructions";
import PetsInstructions from "@/web/pages/Dashboard/HomeInstructions/PetsInstructions";
import TrashInstructions from "@/web/pages/Dashboard/HomeInstructions/TrashInstructions";
import OtherInstructions from "@/web/pages/Dashboard/HomeInstructions/OtherInstructions";
import SecurityInstructions from "@/web/pages/Dashboard/HomeInstructions/SecurityInstructions";
import HomeInstructionsReview from "@/web/pages/Dashboard/HomeInstructions/HomeInstructionsReview";

import HomeDocuments from "@/web/pages/Dashboard/HomeDocuments/HomeDocuments";
import Utility from "@/web/pages/Dashboard/HomeDocuments/Utility";
import Gas from "@/web/pages/Dashboard/HomeDocuments/Gas";
import Water from "@/web/pages/Dashboard/HomeDocuments/Water";    

import WillInstructions from "@/web/pages/Dashboard/WillInstructions/WillInstructions";
import LocationInstructions from "@/web/pages/Dashboard/WillInstructions/LocationInstructions";
import LegalInstructions from "@/web/pages/Dashboard/WillInstructions/LegalInstructions";
import WillInstructionsReview from "@/web/pages/Dashboard/WillInstructions/WillInstructionsReview";

import FuneralArrangements from "@/web/pages/Dashboard/FuneralArrangements/FuneralArrangements";
import FuneralDetails from "@/web/pages/Dashboard/FuneralArrangements/FuneralDetails";
import CeremonyLocation from "@/web/pages/Dashboard/FuneralArrangements/CeremonyLocation";
import FuneralClergy from "@/web/pages/Dashboard/FuneralArrangements/FuneralClergy";
import FuneralNotification from "@/web/pages/Dashboard/FuneralArrangements/FuneralNotification";
import FuneralProceedings from "@/web/pages/Dashboard/FuneralArrangements/FuneralProceedings";
import FuneralArrangementReview from "@/web/pages/Dashboard/FuneralArrangements/FuneralArrangementReview";

import ImportantContact from "@/web/pages/Dashboard/ImportantContact/ImportantContact";
import FriendsFamily from "@/web/pages/Dashboard/ImportantContact/FriendsFamily";
import WorkInstructions from "@/web/pages/Dashboard/ImportantContact/WorkInstructions"
import ReligiousAffiliation from "@/web/pages/Dashboard/ImportantContact/ReligiousAffiliation"
import ClubsInstructions from "@/web/pages/Dashboard/ImportantContact/ClubsInstructions"
import ImportantContactReview from "@/web/pages/Dashboard/ImportantContact/ImportantContactReview"

import SocialMedia from "@/web/pages/Dashboard/SocialMedia/SocialMedia";
import Email from "@/web/pages/Dashboard/SocialMedia/Email";
import Facebook from "@/web/pages/Dashboard/SocialMedia/FaceBook";
import Instagram from "@/web/pages/Dashboard/SocialMedia/Instagram";
import OtherSocial from "@/web/pages/Dashboard/SocialMedia/OtherSocial";
import CellPhone from "@/web/pages/Dashboard/SocialMedia/CellPhone";
import SocialMediaReview from "@/web/pages/Dashboard/SocialMedia/SocialMediaReview";

import TrashCollection from "@/web/pages/Dashboard/HomeDocuments/TrashCollection";
import Hvac from "@/web/pages/Dashboard/HomeDocuments/HVAC";
import Pest from "@/web/pages/Dashboard/HomeDocuments/Pest";
import LawnCare from "@/web/pages/Dashboard/HomeDocuments/LawnCare";
import Cable from "@/web/pages/Dashboard/HomeDocuments/Cable";
import Internet from "@/web/pages/Dashboard/HomeDocuments/Internet";
import HomeDocumentsReview from "@/web/pages/Dashboard/HomeDocuments/HomeDocumentsReview";

function CategoryStartupWrapper() {
  const { categoryName } = useParams();
  return <CategoryStartup category={categoryName} />;
}

function CategoryInfoWrapper() {
  const { categoryName } = useParams();
  if (categoryName === 'willinstructions') {
    return <WillInstructions category={categoryName} />;
  }
  if (categoryName === 'funeralarrangements') {
    return <FuneralArrangements />;
  }
  if (categoryName === 'importantcontacts') {
    return <ImportantContact />;
  }
  if (categoryName === 'socialmedia') {
    return <SocialMedia />;
  }
  if (categoryName === 'homedocuments') {
    return <HomeDocuments />;
  }
  return <HomeInstructions category={categoryName} />;
}

// Wrapper for mobile info page
function InfoPageWrapper() {
  const { categoryName } = useParams();
  if (categoryName === 'willlocation' || categoryName === 'willinstructions') {
    return <WillInstructionsPage />;
  }
  if (categoryName === 'funeralarrangements') {
    return <FuneralArrangementsPage />;
  }
  if (categoryName === 'socialmediaphone') {
    return <SocialMediaPhonePage />;
  }
  if (categoryName === 'importantcontacts') {
    return <ContactPage />;
  }
  if (categoryName === 'homedocuments') {
    return <HomeDocumentsPage />;
  }
  if (categoryName === 'socialmedia') {
    return <SocialMediaPhonePage />;
  }
  return <HomeInstructionsPage />;
}

export default function App() {
  const isMobile = useMediaQuery("(max-width: 768px)");

  return (
    <Router>
      <AuthProvider>
        <Routes>
          {isMobile ? (
          // Mobile routes
          <>
            <Route path="/" element={<LandingPage />}/>
            <Route path="/auth/get-started" element={<Layout><SplashPage /></Layout>} />
            <Route path="/auth/login" element={<Layout><LoginPage /></Layout>} />
            <Route path="/auth/register" element={<Layout><RegisterPage /></Layout>} />
            <Route path="/auth/user-profile" element={<ProtectedRoute><Layout><UserProfile /></Layout></ProtectedRoute>} />
            <Route path="/auth/resetpassword" element={<Layout><ResetPassword /></Layout>} />
            <Route path="/auth/forgetpassword" element={<Layout><ForgetPassword /></Layout>} />
            <Route path="/auth/verify" element={<Layout><VerificationForm /></Layout>} />
            <Route path="/auth/verify-email" element={<Layout><EmailVerifyPage /></Layout>} />
            <Route path="/auth/google/callback" element={<GoogleAuthCallback />} />
            <Route path="/auth/subscribe" element={<Layout><SubscribePage /></Layout>} />

            <Route path="/dashboard" element={<ProtectedRoute><Layout><DashboardPage /></Layout></ProtectedRoute>} />

            <Route path="/category/:categoryId" element={<Layout><CategoryConfirmPage /></Layout>} />

            <Route path="/category/:categoryName/info" element={<Layout><InfoPageWrapper /></Layout>} />
            <Route path="/category/:categoryName/homelocation" element={<Layout><HomeLocationInstructionsPage /></Layout>} />
            <Route path="/category/:categoryName/pets" element={<Layout><PetsInstructionsPage /></Layout>} />
            <Route path="/category/:categoryName/trash" element={<Layout><TrashInstructionsPage /></Layout>} />
            <Route path="/category/:categoryName/other" element={<Layout><OtherInstructionsPage /></Layout>} />
            <Route path="/category/:categoryName/security" element={<Layout><SecurityInstructionsPage /></Layout>} />
            <Route path="/category/homeinstructions/review" element={<Layout><HomeInstructionsReviewPage /></Layout>} />

            <Route path="/category/:categoryName/utility" element={<Layout><UtilityPage /></Layout>} />
            <Route path="/category/:categoryName/gas" element={<Layout><GasPage /></Layout>} />
            <Route path="/category/:categoryName/water" element={<Layout><WaterPage /></Layout>} />
            <Route path="/category/:categoryName/trashcollection" element={<Layout><TrashPage /></Layout>} />
            <Route path="/category/:categoryName/hvac" element={<Layout><HvacPage /></Layout>} />
            <Route path="/category/:categoryName/pest" element={<Layout><PestPage /></Layout>} />
            <Route path="/category/:categoryName/lawn" element={<Layout><LawnPage /></Layout>} />
            <Route path="/category/:categoryName/cable" element={<Layout><CablePage /></Layout>} />
            <Route path="/category/:categoryName/internet" element={<Layout><InternetPage /></Layout>} />
            <Route path="/category/:categoryName/review" element={<Layout><HomeDocumentsReviewPage /></Layout>} />
            
            <Route path="/category/willinstructions/location" element={<Layout><LocationInstrucationsPage /></Layout>} />
            <Route path="/category/willinstructions/legal" element={<Layout><LegalInstructionsPage /></Layout>} />
            <Route path="/category/willinstructions/review" element={<Layout><WillInstructionsReviewPage /></Layout>} />

            <Route path="/category/:categoryName/details" element={<Layout><FuneralDetailsPage /></Layout>} />
            <Route path="/category/:categoryName/ceremonylocation" element={<Layout><CeremonyLocationPage /></Layout>} />
            <Route path="/category/:categoryName/clergy" element={<Layout><FuneralClergyPage /></Layout>} />
            <Route path="/category/:categoryName/notification" element={<Layout><FuneralNotificationPage /></Layout>} />
            <Route path="/category/:categoryName/proceedings" element={<Layout><FuneralProceedingsPage /></Layout>} />
            <Route path="/category/funeralarrangements/review" element={<Layout><FuneralArrangementReviewPage /></Layout>} />

            <Route path="/category/:categoryName/friendsfamily" element={<Layout><FriendsandFamily /></Layout>} />
            <Route path="/category/:categoryName/work" element={<Layout><WorkPage /></Layout>} />
            <Route path="/category/:categoryName/religiousaffiliation" element={<Layout><ReligiousAffiliationPage /></Layout>} />
            <Route path="/category/:categoryName/clubs" element={<Layout><ClubsPage /></Layout>} />
            <Route path="/category/importantcontacts/review" element={<Layout><ContactReviewPage /></Layout>} /> 
            
            <Route path="/category/:categoryName/email" element={<Layout><EmailPage /></Layout>} />
            <Route path="/category/:categoryName/facebook" element={<Layout><FacebookPage /></Layout>} />
            <Route path="/category/:categoryName/instagram" element={<Layout><InstagramPage /></Layout>} />
            <Route path="/category/:categoryName/otheraccounts" element={<Layout><OtherAccountsPage /></Layout>} />
            <Route path="/category/:categoryName/cellphone" element={<Layout><CellPhonePage /></Layout>} />
            <Route path="/category/socialmedia/review" element={<Layout><SocialMediaPhoneReviewPage /></Layout>} />         
          
          </>
        ) : (
          // Web routes
          <>
            <Route path="/" element={<WebLandingPage />} />
            <Route path="/auth/get-started" element={<WebSplashPage />} />
            <Route path="/auth/register" element={<WebAuthForm initialMode="register" />} />
            <Route path="/auth/login" element={<WebAuthForm initialMode="login" />} />
            <Route path="/auth/verify" element={<WebLayout><WebVerificationForm /></WebLayout>} />
            <Route path="/auth/resetpassword" element={<WebLayout><WebResetPassword /></WebLayout>} />
            <Route path="/auth/forgetpassword" element={<WebLayout><WebForgetPassword /></WebLayout>} />
            <Route path="/auth/google/callback" element={<GoogleAuthCallback />} />
            <Route path="/auth/subscribe" element={<WebLayout><SubscribePage /></WebLayout>} />
            <Route path="/auth/user-profile" element={<ProtectedRoute><WebLayout><WebUserProfile /></WebLayout></ProtectedRoute>} />
            <Route path="/dashboard" element={<ProtectedRoute><WebLayout><Dashboard /></WebLayout></ProtectedRoute>} />

            <Route path="/invitations" element={<InviteDialog />} />
            <Route path="/accept-invitation/:token" element={<AcceptInvitation />} />
            <Route path="/auth/register/:token" element={<AcceptInvitation />} />
            
            <Route path="/approve-category-request/:token" element={<ApproveCategoryRequest />} />

            <Route path="/category/:categoryName" element={<CategoryStartupWrapper />} />

            <Route path="/category/:categoryName/info" element={<CategoryInfoWrapper />} />
            <Route path="/category/:categoryName/homelocation" element={<HomeLocationInstructions />} />
            <Route path="/category/:categoryName/pets" element={<PetsInstructions />} />
            <Route path="/category/:categoryName/trash" element={<TrashInstructions />} />
            <Route path="/category/:categoryName/other" element={<OtherInstructions />} />
            <Route path="/category/:categoryName/security" element={<SecurityInstructions />} />
            <Route path="/category/homeinstructions/review" element={<HomeInstructionsReview />} />

            <Route path="/category/:categoryName/utility" element={<Utility />} />
            <Route path="/category/:categoryName/gas" element={<Gas />} />
            <Route path="/category/:categoryName/water" element={<Water />} />
            <Route path="/category/:categoryName/trashcollection" element={<TrashCollection />} />
            <Route path="/category/:categoryName/hvac" element={<Hvac />} />
            <Route path="/category/:categoryName/pest" element={<Pest />} />
            <Route path="/category/:categoryName/lawn" element={<LawnCare />} />
            <Route path="/category/:categoryName/cable" element={<Cable />} />
            <Route path="/category/:categoryName/internet" element={<Internet />} />
            <Route path="/category/:categoryName/review" element={<HomeDocumentsReview />} />

            <Route path="/category/:categoryName/location" element={<LocationInstructions />} />
            <Route path="/category/:categoryName/legal" element={<LegalInstructions />} />
            <Route path="/category/willinstructions/review" element={<WillInstructionsReview />} />

            <Route path="/category/:categoryName/details" element={<FuneralDetails />} />
            <Route path="/category/:categoryName/ceremonylocation" element={<CeremonyLocation />} />
            <Route path="/category/:categoryName/clergy" element={<FuneralClergy />} />
            <Route path="/category/:categoryName/notification" element={<FuneralNotification />} />
            <Route path="/category/:categoryName/proceedings" element={<FuneralProceedings />} />
            <Route path="/category/funeralarrangements/review" element={<FuneralArrangementReview />} />

            <Route path="/category/:categoryName/friendsfamily" element={<FriendsFamily />} />
            <Route path="/category/:categoryName/work" element={<WorkInstructions />} />
            <Route path="/category/:categoryName/religiousaffiliation" element={<ReligiousAffiliation />} />
            <Route path="/category/:categoryName/clubs" element={<ClubsInstructions />} />
            <Route path="/category/importantcontacts/review" element={<ImportantContactReview />} />

            <Route path="/category/:categoryName/email" element={<Email />} />
            <Route path="/category/:categoryName/facebook" element={<Facebook />} />
            <Route path="/category/:categoryName/instagram" element={<Instagram />} />
            <Route path="/category/:categoryName/otheraccounts" element={<OtherSocial />} />
            <Route path="/category/:categoryName/cellphone" element={<CellPhone />} />
            <Route path="/category/socialmedia/review" element={<SocialMediaReview />} />
          </>

        )}
      </Routes>
      </AuthProvider>
    </Router>
  );
}
