import { Request, Response } from 'express';
import Subcategory from '../models/subcategory';
import mongoose from 'mongoose';

export const subcategory = async (req: Request, res: Response): Promise<void> => {
    try {
        const { name, categoryId } = req.body;

        if (!name || !categoryId) {
            res.status(400).json({ message: 'Subcategory name and category ID are required.' });
            return;
        }

        const subcategory = new Subcategory({ name, categoryId });
        await subcategory.save();
        res.status(201).json(subcategory);
    } catch (error) {
        res.status(500).json({ message: 'Error creating subcategory', error });
    }
};

export const SubcategoriesByCategoryId = async (req: Request, res: Response): Promise<void> => {
    try {
        const { categoryId } = req.query;

        if (!categoryId) {
            res.status(400).json({ message: 'Category ID is required.' });
            return;
        }

        console.log(`Received request for subcategories with categoryId: ${categoryId}`);

        // Try to find the category using multiple strategies
        let category;
        let query;

        try {
            // Strategy 1: If categoryId is a valid ObjectId, use it directly
            if (mongoose.Types.ObjectId.isValid(categoryId as string)) {
                console.log(`Trying to find category by ObjectId: ${categoryId}`);
                const objId = new mongoose.Types.ObjectId(categoryId as string);
                category = await mongoose.model('Category').findById(objId);
                if (category) {
                    console.log(`Found category by ObjectId: ${category.name}`);
                    query = { categoryId: objId };
                }
            }

            // Strategy 2: If not found by ObjectId, try to find by name
            if (!category) {
                console.log(`Trying to find category by name containing: ${categoryId}`);
                category = await mongoose.model('Category').findOne({
                    name: new RegExp(categoryId as string, 'i')
                });
                if (category) {
                    console.log(`Found category by name: ${category.name}`);
                    query = { categoryId: category._id };
                }
            }

            // Strategy 3: If numeric ID, try to find by numericId field
            if (!category && /^\d+$/.test(categoryId as string)) {
                console.log(`Trying to find category by numericId: ${categoryId}`);
                const numericId = parseInt(categoryId as string, 10);
                category = await mongoose.model('Category').findOne({ numericId });
                if (category) {
                    console.log(`Found category by numericId: ${category.name}`);
                    query = { categoryId: category._id };
                } else {
                    // Fallback: Try to find by index position
                    console.log(`Trying to find category by index position: ${categoryId}`);
                    const allCategories = await mongoose.model('Category').find().sort({ createdAt: 1 });
                    if (numericId > 0 && numericId <= allCategories.length) {
                        category = allCategories[numericId - 1];
                        if (category) {
                            console.log(`Found category by index: ${category.name}`);
                            query = { categoryId: category._id };
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error finding category:', error);
        }

        if (!query) {
            console.log(`Category not found for ID: ${categoryId}`);
            res.status(404).json({ message: 'Category not found' });
            return;
        }

        const subcategories = await Subcategory.find(query);
        res.json(subcategories);
    } catch (error) {
        console.error('Error fetching subcategories:', error);
        res.status(500).json({ message: 'Error fetching subcategories', error });
    }
};

export const getSubcategoryDetailsQuestions = async (req: Request, res: Response): Promise<void> => {
    try {
        const { categoryId } = req.query;

        if (!categoryId) {
            res.status(400).json({ message: 'Category ID is required.' });
            return;
        }

        console.log(`Received request for subcategories with categoryId: ${categoryId}`);

        // Try to find the category using multiple strategies
        let category;
        let matchQuery;

        try {
            // Strategy 1: If categoryId is a valid ObjectId, use it directly
            if (mongoose.Types.ObjectId.isValid(categoryId as string)) {
                console.log(`Trying to find category by ObjectId: ${categoryId}`);
                const objId = new mongoose.Types.ObjectId(categoryId as string);
                category = await mongoose.model('Category').findById(objId);
                if (category) {
                    console.log(`Found category by ObjectId: ${category.name}`);
                    matchQuery = { categoryId: objId };
                }
            }

            // Strategy 2: If not found by ObjectId, try to find by name
            if (!category) {
                console.log(`Trying to find category by name containing: ${categoryId}`);
                category = await mongoose.model('Category').findOne({
                    name: new RegExp(categoryId as string, 'i')
                });
                if (category) {
                    console.log(`Found category by name: ${category.name}`);
                    matchQuery = { categoryId: category._id };
                }
            }

            // Strategy 3: If numeric ID, try to find by numericId field
            if (!category && /^\d+$/.test(categoryId as string)) {
                console.log(`Trying to find category by numericId: ${categoryId}`);
                const numericId = parseInt(categoryId as string, 10);
                category = await mongoose.model('Category').findOne({ numericId });
                if (category) {
                    console.log(`Found category by numericId: ${category.name}`);
                    matchQuery = { categoryId: category._id };
                } else {
                    // Fallback: Try to find by index position
                    console.log(`Trying to find category by index position: ${categoryId}`);
                    const allCategories = await mongoose.model('Category').find().sort({ createdAt: 1 });
                    if (numericId > 0 && numericId <= allCategories.length) {
                        category = allCategories[numericId - 1];
                        if (category) {
                            console.log(`Found category by index: ${category.name}`);
                            matchQuery = { categoryId: category._id };
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error finding category:', error);
        }

        if (!matchQuery) {
            console.log(`Category not found for ID: ${categoryId}`);
            res.status(404).json({ message: 'Category not found' });
            return;
        }

        const subcategories = await Subcategory.aggregate([
            {
                $match: matchQuery
            },
            {
                $lookup: {
                    from: 'questions',
                    localField: '_id',
                    foreignField: 'subCategoryId',
                    as: 'questions'
                }
            },
            {
                $addFields: {
                    questions: { $arrayElemAt: ['$questions', 0] }
                }
            }, 
            
        ]);
        res.status(200).json(subcategories);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching subcategories with questions', error });
    }
};

// export const getSubcategoriesWithQuestionsAndCategory = async (req: Request, res: Response): Promise<void> => {
//     try {
//         const { categoryId } = req.query;

//         if (!categoryId) {
//             res.status(400).json({ message: 'Category ID is required.' });
//             return;
//         }

//         const subcategories = await Subcategory.aggregate([
//             {
//                 $match: { categoryId: new mongoose.Types.ObjectId(categoryId as string) }
//             },
//             {
//                 $lookup: {
//                     from: 'questions',
//                     localField: '_id',
//                     foreignField: 'subCategoryId',
//                     as: 'questions'
//                 }
//             },
//             {
//                 $addFields: {
//                     questions: { $arrayElemAt: ['$questions', 0] }
//                 }
//             },

//         ]);
//         res.status(200).json(subcategories);
//     } catch (error) {
//         res.status(500).json({ message: 'Error fetching subcategories with questions and category details', error });
//     }
// };


