import mongoose from 'mongoose';
import crypto from 'crypto';
import { IInvitedUser, IInvitedUserModel, InvitationStatus } from '../types/InvitedUser';

const invitedUserSchema = new mongoose.Schema({
  ownerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Owner',
    required: true
  },
  invitedUserId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  status: {
    type: String,
    enum: Object.values(InvitationStatus),
    default: InvitationStatus.PENDING,
    required: true
  },
  relation: {
    type: String,
    required: true,
    trim: true
  },
  invitationToken: {
    type: String,
    default: null
  },
  invitationTokenExpire: {
    type: Date,
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Add indexes for better query performance
invitedUserSchema.index({ ownerId: 1 });
invitedUserSchema.index({ invitedUserId: 1 });
invitedUserSchema.index({ status: 1 });
invitedUserSchema.index({ ownerId: 1, invitedUserId: 1 }, { unique: true }); // Prevent duplicate invitations

// Virtual to populate owner data
invitedUserSchema.virtual('owner', {
  ref: 'Owner',
  localField: 'ownerId',
  foreignField: '_id',
  justOne: true
});

// Virtual to populate invited user data
invitedUserSchema.virtual('invitedUser', {
  ref: 'User',
  localField: 'invitedUserId',
  foreignField: '_id',
  justOne: true
});

// Static method to find invitations by owner ID
invitedUserSchema.statics.findByOwnerId = function(ownerId: mongoose.Types.ObjectId) {
  return this.find({ ownerId }).populate('owner').populate('invitedUser');
};

// Static method to find invitations by user ID
invitedUserSchema.statics.findByUserId = function(userId: mongoose.Types.ObjectId) {
  return this.find({ invitedUserId: userId }).populate('owner').populate('invitedUser');
};

// Static method to find pending invitations
invitedUserSchema.statics.findPendingInvitations = function(ownerId?: mongoose.Types.ObjectId) {
  const query = { status: InvitationStatus.PENDING };
  if (ownerId) {
    (query as any).ownerId = ownerId;
  }
  return this.find(query).populate('owner').populate('invitedUser');
};

// Instance method to generate invitation token
invitedUserSchema.methods.generateInvitationToken = function() {
  const token = crypto.randomBytes(32).toString('hex');
  const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

  this.invitationToken = hashedToken;
  this.invitationTokenExpire = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

  return token; // return the raw token (unhashed) to be sent via email
};

const InvitedUser = mongoose.model<IInvitedUser, IInvitedUserModel>('InvitedUser', invitedUserSchema);

export default InvitedUser;
