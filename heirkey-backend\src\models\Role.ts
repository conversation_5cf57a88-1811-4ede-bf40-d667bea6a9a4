import mongoose from 'mongoose';
import { IRole, RoleType, IRoleModel } from '../types/Role';

const roleSchema = new mongoose.Schema({
  name: {
    type: String,
    enum: Object.values(RoleType),
    required: true,
    unique: true
  },
  description: {
    type: String,
    required: true
  },
  permissions: [{
    type: String,
    required: true
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, { timestamps: true });

// Add text index for name field for search capabilities
roleSchema.index({ name: 'text' });

// Static method to get default roles
roleSchema.statics.getDefaultRoles = function() {
  return [
    {
      name: RoleType.OWNER,
      description: 'Primary account holder with full access to all features and data',
      permissions: [
        'canViewAll',
        'canEditAll',
        'canDeleteAll',
        'canCreateAll'
      ],
      isActive: true
    },
    {
      name: RoleType.NOMINEE,
      description: 'Designated beneficiary with view-only access to information',
      permissions: [
        'canViewAll'
      ],
      isActive: true
    },
    {
      name: RoleType.FAMILY,
      description: 'Family members with full access to all features and data',
      permissions: [
        'canViewAll',
        'canEditAll',
        'canDeleteAll',
        'canCreateAll'
      ],
      isActive: true
    }
  ];
};

// Instance method to check if role has specific permission
roleSchema.methods.hasPermission = function(permission: string): boolean {
  return this.permissions.includes(permission);
};

const Role = mongoose.model<IRole, IRoleModel>('Role', roleSchema);

export default Role;

