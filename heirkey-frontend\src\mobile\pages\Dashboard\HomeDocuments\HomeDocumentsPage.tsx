import { useAuth } from '@/contexts/AuthContext';
import GradiantHeader from "@/mobile/components/header/gradiantHeader";
import Footer from "@/mobile/components/layout/Footer";
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { fetchUserInputs, selectError, selectLoading, selectUserInputs } from '@/store/slices/homeDocumentsSlice';
import { CheckCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';
import homeDocumentsData from '@/data/homeDocuments.json';

const labelToId: Record<string, string> = {
  Utility: '301',
  Gas: '302',
  Water: '303',
  Trash: '304',
  HVAC: '305',
  Pest: '306',
  Lawn: '307',
  Cable: '308',
  Internet: '309',
};

// Helper to get question count for each subcategory
const getQuestionCount = (label: string) => {
  switch (label) {
    case 'Utility': return (homeDocumentsData['301'] || []).length;
    case 'Gas': return (homeDocumentsData['302'] || []).length;
    case 'Water': return (homeDocumentsData['303'] || []).length;
    case 'Trash': return (homeDocumentsData['304'] || []).length;
    case 'HVAC': return (homeDocumentsData['305'] || []).length;
    case 'Pest': return (homeDocumentsData['306'] || []).length;
    case 'Lawn': return (homeDocumentsData['307'] || []).length;
    case 'Cable': return (homeDocumentsData['308'] || []).length;
    case 'Internet': return (homeDocumentsData['309'] || []).length;
    default: return 0;
  }
};

export default function HomeDocumentsPage() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const userInputs = useAppSelector(selectUserInputs);
  const loading = useAppSelector(selectLoading);
  const error = useAppSelector(selectError);
  const [localError, setError] = useState<string | null>(null);

  // Fetch user inputs when component mounts using owner ID
  useEffect(() => {
    const fetchData = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      }
    };

    fetchData();
  }, [dispatch, user]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <GradiantHeader title="Home Documents" showAvatar={true} />
        <div className="container mx-auto px-4 py-6 text-center">
          Loading your home documents...
        </div>
      </div>
    );
  }

  if (localError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <GradiantHeader title="Home Documents" showAvatar={true} />
        <div className="container mx-auto px-4 py-6 text-center text-red-500">
          {localError}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <GradiantHeader 
        title="Home Documents" 
        showAvatar={true}
      />
      
      <div className="container mx-auto px-4 py-6 max-w-md">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-5 mb-6">
          <h2 className="text-lg font-bold text-[#8B5CF6] mb-2">Understanding Topics</h2>
          <p className="text-sm text-gray-600">
            Each topic below is a part of your home documents, with questions to help you provide important information for you and your loved ones. Click any topic to answer the questions at your own pace—we'll save everything for you.
          </p>
        </div>

        <div className="space-y-3">
          {categoryTabsConfig.homedocuments.map((item) => {
            const total = getQuestionCount(item.label);
            const completedQuestions = userInputs.reduce((count, input) => {
              if (input.originalSubCategoryId === labelToId[item.label] || 
                  input.originalSubCategoryId === `${labelToId[item.label]}A`) {
                return count + input.answersBySection.reduce(
                  (sectionCount, section) => sectionCount + section.answers.length, 0
                );
              }
              return count;
            }, 0);

            const totalQuestions = total;
            const isActive = activeTab === item.label;
            const isCompleted = completedQuestions >= totalQuestions && totalQuestions > 0;

            return (
              <div
                key={item.label}
                className={
                  "flex items-center justify-between px-4 py-3 rounded-xl border transition-all " +
                  (isActive
                    ? "border-[#2BCFD5] bg-white shadow"
                    : "border-gray-200 bg-gray-50 hover:bg-white")
                }
                style={{ cursor: "pointer" }}
                onClick={() => {
                  setActiveTab(item.label);
                  navigate(`/category/homedocuments/${item.label.toLowerCase().replace(/ /g, '')}`);
                }}
              >
                <span className="font-medium text-gray-900">{item.label}</span>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-gray-500">
                    {completedQuestions}/{totalQuestions} questions
                  </span>
                  {isCompleted && (
                    <>
                      <CheckCircle className="w-5 h-5 text-[#2BCFD5]" />
                      <button
                        className="text-xs text-[#2BCFD5] font-semibold px-2 py-1 rounded hover:underline"
                        onClick={e => { e.stopPropagation(); navigate(`/category/homedocuments/${item.label.toLowerCase().replace(/ /g, '')}`); }}
                      >
                        Edit
                      </button>
                    </>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
      <Footer />
    </div>
  );
}
