import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { fetchMyRequests, fetchOwnerRequests } from '../../store/slices/requestedCategoriesSlice';
import { RootState, useAppDispatch } from '../../store';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CircularProgress } from '@/components/ui/CircularProgress';
import { RequestedCategory } from '@/types/requestedCategories';
import { getCategoryName } from '@/constants/categories';

interface RequestListProps {
  isOwner?: boolean;
}

const RequestList: React.FC<RequestListProps> = ({ isOwner = false }) => {
  const dispatch = useAppDispatch();
  const { requests, loading, error } = useSelector((state: RootState) => state.requestedCategories);

  useEffect(() => {
    if (isOwner) {
      dispatch(fetchOwnerRequests());
    } else {
      dispatch(fetchMyRequests());
    }
  }, [dispatch, isOwner]);

  const getStatusVariant = (status: string): "default" | "destructive" | "secondary" => {
    switch (status) {
      case 'approved':
        return 'default';
      case 'rejected':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center p-6">
        <CircularProgress value={0} max={100} />
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="m-4">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (requests.length === 0) {
    return (
      <div className="p-6 text-center text-muted-foreground">
        No requests found
      </div>
    );
  }

  return (
    <div className="max-w-3xl mx-auto p-4">
      <h2 className="text-2xl font-semibold mb-6">
        {isOwner ? 'Category Access Requests' : 'My Category Requests'}
      </h2>

      <div className="space-y-4">
        {requests.map((request: RequestedCategory) => (
          <Card key={request.id}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-lg">
                {isOwner ? `Request from: ${request.requester?.name}` : 'Requested Categories'}
              </CardTitle>
              <Badge variant={getStatusVariant(request.status)}>
                {request.status.toUpperCase()}
              </Badge>
            </CardHeader>
            <CardContent>
              <Separator className="my-4" />

              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">
                    Categories:
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {request.categoryIds.map(categoryId => (
                      <Badge
                        key={categoryId}
                        variant="outline"
                      >
                        {getCategoryName(categoryId)}
                      </Badge>
                    ))}
                  </div>
                </div>

                {request.requestMessage && (
                  <div>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">
                      Message:
                    </h4>
                    <p className="text-sm">
                      {request.requestMessage}
                    </p>
                  </div>
                )}

                <p className="text-xs text-muted-foreground">
                  Requested on: {new Date(request.createdAt).toLocaleDateString()}
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default RequestList; 