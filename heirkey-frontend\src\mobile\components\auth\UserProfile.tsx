import { Alert, AlertDescription } from '@/components/ui/alert';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/AuthContext';
import authService from '@/services/authService';
import { UserPen, Loader2 } from 'lucide-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import { useNavigate } from 'react-router-dom';
import Select from 'react-select';
import AuthHeader from '../header/gradiantHeader';
// Import the country list package
import countryList from 'react-select-country-list';

// Define the option type for react-select
interface CountryOption {
  value: string;
  label: string;
}

export default function UserProfile() {
  const { user, logout, isLoading, setUser } = useAuth();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [firstName, setFirstName] = useState(user?.firstName || '');
  const [lastName, setLastName] = useState(user?.lastName || '');
  const [email, setEmail] = useState(user?.email || '');
  const [username, setUsername] = useState(user?.username || '');
  const [phone, setPhone] = useState(user?.phone || '');
  const [address, setAddress] = useState(user?.address || '');
  const [zipCode, setZipCode] = useState(user?.zipCode || '');
  const [country, setCountry] = useState(user?.country || '');

  // Get country options using the countryList package
  const countryOptions = useMemo(() => countryList().getData(), []);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(user?.image || null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // For real-time validation
  const [phoneError, setPhoneError] = useState<string | null>(null);
  const [zipCodeError, setZipCodeError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      setFirstName(user.firstName || '');
      setLastName(user.lastName || '');
      setEmail(user.email || '');
      setUsername(user.username || '');
      setPhone(user.phone || '');
      setAddress(user.address || '');
      setZipCode(user.zipCode || '');
      setCountry(user.country || '');
      if (user.image) {
        setImagePreview(user.image);
      }
    }
  }, [user]);

  // Cleanup timeouts on component unmount
  useEffect(() => {
    return () => {
      // Clear all pending timeouts when component unmounts
      Object.values(timeoutRef.current).forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setProfileImage(file);

      // Create a preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Validation functions
  const validatePhone = (value: string): boolean => {
    // With react-phone-input-2, the validation is handled by the component
    // We just need to check if the phone number has a minimum length
    const isValid = value && value.length >= 8 ? true : false;
    setPhoneError(isValid ? null : 'Please enter a valid phone number');
    return isValid;
  };

  const validateZipCode = (value: string): boolean => {
    // Basic ZIP code validation - can be enhanced based on country
    const zipRegex = /^[0-9a-zA-Z\s\-]{3,10}$/;
    const isValid = zipRegex.test(value);
    setZipCodeError(isValid ? null : 'Please enter a valid ZIP/postal code');
    return isValid;
  };

  // Create a reference for timeout IDs
  const timeoutRef = useRef<Record<string, NodeJS.Timeout>>({});

  // Debounced update function for real-time syncing
  const debouncedUpdate = (field: string, value: string) => {
    // Clear any existing timeout for this field
    if (timeoutRef.current[field]) {
      clearTimeout(timeoutRef.current[field]);
    }

    // Set a new timeout (700ms delay)
    timeoutRef.current[field] = setTimeout(async () => {
      try {
        const updateData: Record<string, string> = { [field]: value };
        const updatedUser = await authService.updateProfile(updateData);
        setUser(updatedUser);
      } catch (err) {
        console.error(`Error updating ${field}:`, err);
      }
    }, 700); // Wait for 700ms of inactivity before making the API call
  };

  // Handle field changes with validation and real-time update
  const handleFieldChange = (field: string, value: string) => {
    switch (field) {
      case 'phone':
        setPhone(value);
        if (validatePhone(value)) {
          debouncedUpdate(field, value);
        }
        break;
      case 'zipCode':
        setZipCode(value);
        if (validateZipCode(value)) {
          debouncedUpdate(field, value);
        }
        break;
      case 'address':
        setAddress(value);
        debouncedUpdate(field, value);
        break;
      case 'country':
        setCountry(value);
        debouncedUpdate(field, value);
        break;
      default:
        break;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isEditing) return;
    setIsSubmitting(true);
    setError(null);
    setSuccess(null);

    // Validate fields
    const isPhoneValid = phone ? validatePhone(phone) : true;
    const isZipValid = zipCode ? validateZipCode(zipCode) : true;

    if (!isPhoneValid || !isZipValid) {
      setIsSubmitting(false);
      setError('Please correct the errors before submitting');
      return;
    }

    try {
      // First update the profile image if changed
      if (profileImage) {
        const formData = new FormData();
        formData.append('image', profileImage);
        const updatedUser = await authService.updateProfileImage(formData);
        setUser(updatedUser);
      }

      // Then update the profile information
      const updatedUser = await authService.updateProfile({
        firstName: firstName || undefined,
        lastName: lastName || undefined,
        username: username || undefined,
        phone: phone || undefined,
        address: address || undefined,
        zipCode: zipCode || undefined,
        country: country || undefined
      });

      setUser(updatedUser);
      setSuccess('Profile updated successfully!');
      setIsEditing(false);
      navigate('/dashboard');
    } catch (err: unknown) {
      console.error('Profile update error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update profile';
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/auth/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Get initials for avatar fallback
  const getInitials = () => {
    if (firstName && lastName) {
      return `${firstName[0]}${lastName[0]}`.toUpperCase();
    } else if (username) {
      return username.substring(0, 2).toUpperCase();
    }
    return 'UN';
  };

  return (
    <>
      <AuthHeader title={`Welcome, ${firstName || username || 'User'}`} />

      <div className="min-h-screen py-4 px-3 flex justify-center items-start">
        <Card className="w-full max-w-md shadow">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-lg text-gray-900">Profile Settings</CardTitle>
              {!isEditing && (
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1 h-8 px-2"
                  onClick={() => setIsEditing(true)}
                  disabled={isLoading}
                >
                  <UserPen size={14} /> Edit
                </Button>
              )}
            </div>
          </CardHeader>

          <CardContent className="pt-2">
            {error && (
              <Alert variant="destructive" className="mb-3 py-2">
                <AlertDescription className="text-sm">{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="mb-3 py-2 bg-green-50 border-green-200">
                <AlertDescription className="text-sm text-green-700">{success}</AlertDescription>
              </Alert>
            )}

            <form className="space-y-3" onSubmit={handleSubmit}>
              {/* Profile Image and Basic Info */}
              <div className="flex items-center gap-4 mb-1">
                <div className="relative group">
                  <Avatar className="h-16 w-16 border-2 border-[#22BBCC] shadow">
                    {imagePreview ? (
                      <AvatarImage src={imagePreview} alt="Profile" className="object-cover" />
                    ) : null}
                    <AvatarFallback>{getInitials()}</AvatarFallback>
                  </Avatar>
                  {isEditing && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <Label htmlFor="profileImage" className="cursor-pointer text-white text-xs">
                        Change
                      </Label>
                    </div>
                  )}
                </div>
                <div>
                  <h3 className="font-medium">{firstName} {lastName}</h3>
                  <p className="text-xs text-gray-500">{email}</p>
                  {isEditing && (
                    <>
                      <Label
                        htmlFor="profileImage"
                        className="cursor-pointer text-xs text-[#22BBCC] hover:text-[#22BBCA]"
                      >
                        Change Profile Picture
                      </Label>
                      <Input
                        id="profileImage"
                        name="profileImage"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleImageChange}
                      />
                    </>
                  )}
                </div>
              </div>

              {/* Form Fields */}
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1">
                  <Label htmlFor="firstName" className="text-xs font-medium text-gray-700">
                    First Name
                  </Label>
                  <Input
                    id="firstName"
                    name="firstName"
                    type="text"
                    placeholder="First name"
                    className="h-9 text-sm"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    readOnly={!isEditing}
                  />
                </div>

                <div className="space-y-1">
                  <Label htmlFor="lastName" className="text-xs font-medium text-gray-700">
                    Last Name
                  </Label>
                  <Input
                    id="lastName"
                    name="lastName"
                    type="text"
                    placeholder="Last name"
                    className="h-9 text-sm"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    readOnly={!isEditing}
                  />
                </div>
              </div>

              <div className="space-y-1">
                <Label htmlFor="phone" className="text-xs font-medium text-gray-700">
                  Phone Number
                </Label>
                <PhoneInput
                  country={'us'}
                  value={phone}
                  onChange={(value) => isEditing && handleFieldChange('phone', value)}
                  inputProps={{
                    name: 'phone',
                    id: 'phone',
                    required: true,
                    readOnly: !isEditing,
                    disabled: !isEditing,
                  }}
                  containerClass={`${phoneError ? 'border-red-500' : ''}`}
                  inputClass="w-full text-sm"
                  buttonClass="h-9"
                  containerStyle={{ height: '36px' }}
                  disabled={!isEditing}
                />
                {phoneError && <p className="text-xs text-red-500">{phoneError}</p>}
              </div>

              <div className="space-y-1">
                <Label htmlFor="address" className="text-xs font-medium text-gray-700">
                  Address
                </Label>
                <Input
                  id="address"
                  name="address"
                  type="text"
                  placeholder="Full address"
                  className="h-9 text-sm"
                  value={address}
                  onChange={(e) => handleFieldChange('address', e.target.value)}
                  readOnly={!isEditing}
                />
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1">
                  <Label htmlFor="zipCode" className="text-xs font-medium text-gray-700">
                    ZIP/Postal Code
                  </Label>
                  <Input
                    id="zipCode"
                    name="zipCode"
                    type="text"
                    placeholder="ZIP code"
                    className={`h-9 text-sm ${zipCodeError ? 'border-red-500' : ''}`}
                    value={zipCode}
                    onChange={(e) => handleFieldChange('zipCode', e.target.value)}
                    onBlur={(e) => validateZipCode(e.target.value)}
                    readOnly={!isEditing}
                  />
                  {zipCodeError && <p className="text-xs text-red-500">{zipCodeError}</p>}
                </div>

                <div className="space-y-1">
                  <Label htmlFor="country" className="text-xs font-medium text-gray-700">
                    Country
                  </Label>
                  <Select<CountryOption>
                    id="country"
                    name="country"
                    options={countryOptions}
                    placeholder="Select country"
                    className="text-sm"
                    classNamePrefix="select"
                    value={countryOptions.find((option: CountryOption) => option.label === country) || null}
                    onChange={isEditing ? (selectedOption: CountryOption | null) => {
                      if (selectedOption) {
                        handleFieldChange('country', selectedOption.label);
                      }
                    } : undefined}
                    isDisabled={!isEditing}
                    styles={{
                      control: (baseStyles: any) => ({
                        ...baseStyles,
                        minHeight: '36px',
                        height: '36px',
                        borderColor: '#e2e8f0',
                        backgroundColor: !isEditing ? '#f3f4f6' : 'white',
                      }),
                      valueContainer: (base: any) => ({
                        ...base,
                        padding: '0 8px',
                      }),
                      input: (base: any) => ({
                        ...base,
                        margin: '0',
                        padding: '0',
                      }),
                      menu: (baseStyles: any) => ({
                        ...baseStyles,
                        zIndex: 50,
                      }),
                    }}
                  />
                </div>
              </div>

              <div className="flex justify-end pt-2">
                {isEditing && (
                  <div className="flex gap-2 w-full">
                    <Button
                      type="button"
                      variant="outline"
                      className="h-9 px-4 w-1/2"
                      onClick={() => {
                        setIsEditing(false);
                        setFirstName(user?.firstName || '');
                        setLastName(user?.lastName || '');
                        setEmail(user?.email || '');
                        setUsername(user?.username || '');
                        setPhone(user?.phone || '');
                        setAddress(user?.address || '');
                        setZipCode(user?.zipCode || '');
                        setCountry(user?.country || '');
                        setImagePreview(user?.image || null);
                        setProfileImage(null);
                        setError(null);
                        setSuccess(null);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="h-9 px-4 w-1/2 bg-[#22BBCC] text-white hover:bg-[#22BBCA]"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-1 h-3 w-3 animate-spin" /> Saving...
                        </>
                      ) : (
                        "Update Profile"
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
