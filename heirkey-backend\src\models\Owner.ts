import mongoose from 'mongoose';
import { IOwner, IOwnerModel } from '../types/Owner';

const ownerSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true // Each user can only have one owner record
  },
  username: {
    type: String,
    trim: true
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  firstName: {
    type: String,
    trim: true
  },
  lastName: {
    type: String,
    trim: true
  },
  image: {
    type: String
  },
  googleId: {
    type: String
  },
  externalUser: {
    type: Boolean,
    default: false
  },
  subscribedPlanId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SubscribedPlan',
    default: null
  }
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Add indexes for better query performance
ownerSchema.index({ userId: 1 });
ownerSchema.index({ email: 1 });
ownerSchema.index({ username: 1 });
ownerSchema.index({ subscribedPlanId: 1 });

// Virtual to populate user data
ownerSchema.virtual('user', {
  ref: 'User',
  localField: 'userId',
  foreignField: '_id',
  justOne: true
});

// Virtual to populate subscription data
ownerSchema.virtual('subscription', {
  ref: 'SubscribedPlan',
  localField: 'subscribedPlanId',
  foreignField: '_id',
  justOne: true
});

// Instance method to get full name
ownerSchema.methods.getFullName = function(): string {
  if (this.firstName && this.lastName) {
    return `${this.firstName} ${this.lastName}`;
  }
  return this.firstName || this.lastName || this.username || 'Unknown';
};

// Static method to find owner by user ID
ownerSchema.statics.findByUserId = function(userId: mongoose.Types.ObjectId) {
  return this.findOne({ userId }).populate('user').populate('subscription');
};

// Static method to find owner by email
ownerSchema.statics.findByEmail = function(email: string) {
  return this.findOne({ email: email.toLowerCase() }).populate('user').populate('subscription');
};

const Owner = mongoose.model<IOwner, IOwnerModel>('Owner', ownerSchema);

export default Owner;

