# Owner Module Documentation

## Overview

The Owner module is a core component of the <PERSON>irkey application that manages owner-specific data and relationships. When a user signs up, the system creates records in both the User model and the Owner model, establishing a bidirectional relationship between them.

## Purpose

The Owner module serves to:
- Store detailed owner information separate from authentication data
- Maintain a clear relationship between users and their owner profiles
- Support the role-based access control system (Owner role)
- Provide a foundation for inheritance and estate management features

## Architecture

### Database Schema

#### Owner Model
```javascript
{
  userId: ObjectId,        // Reference to User model (required, unique)
  username: String,        // Optional username
  email: String,           // Required email (lowercase, trimmed)
  firstName: String,       // Optional first name
  lastName: String,        // Optional last name
  image: String,           // Optional profile image
  googleId: String,        // Optional Google ID for external users
  externalUser: Boolean,   // Default: false
  createdAt: Date,         // Auto-generated
  updatedAt: Date          // Auto-generated
}
```

#### User Model (Updated)
```javascript
{
  // ... existing fields
  roleId: ObjectId,        // Reference to Role model
  ownerId: ObjectId,       // Reference to Owner model (NEW)
  // ... other fields
}
```

### Relationships

1. **User ↔ Owner**: One-to-one bidirectional relationship
   - User.ownerId → Owner._id
   - Owner.userId → User._id

2. **Owner → User**: Virtual population for easy access to user data

3. **User → Role**: Each user has an assigned role (Owner role for signup users)

## Registration Flow

When a new user signs up, the system:

1. **Validates Input**: Checks for existing users and owners with the same email
2. **Finds Owner Role**: Locates the "Owner" role in the database
3. **Creates User**: Creates a new User record with the Owner role assigned
4. **Creates Owner**: Creates a new Owner record linked to the User
5. **Updates User**: Updates the User record with the Owner ID
6. **Transaction Management**: Uses MongoDB transactions to ensure data consistency

### Registration Process (Detailed)

```javascript
// 1. Start transaction
const session = await mongoose.startSession();
session.startTransaction();

// 2. Check for existing user/owner
const existingUser = await User.findOne({ email }).session(session);
const existingOwner = await Owner.findOne({ email }).session(session);

// 3. Find Owner role
const ownerRole = await Role.findOne({ name: RoleType.OWNER }).session(session);

// 4. Create User
const user = new User({
  username, email, password, firstName, lastName,
  roleId: ownerRole._id
});
await user.save({ session });

// 5. Create Owner
const owner = new Owner({
  userId: user._id, username, email, firstName, lastName
});
await owner.save({ session });

// 6. Update User with Owner ID
user.ownerId = owner._id;
await user.save({ session });

// 7. Commit transaction
await session.commitTransaction();
```

## API Endpoints

### Owner Management Routes

All routes require authentication (`/v1/api/owners/`)

#### Get All Owners
```
GET /v1/api/owners/
```
Returns a list of all owners with populated user data.

#### Get Current User's Owner Profile
```
GET /v1/api/owners/profile
```
Returns the owner profile for the currently authenticated user.

#### Update Current User's Owner Profile
```
PUT /v1/api/owners/profile
```
Updates the owner profile for the currently authenticated user.

**Body:**
```json
{
  "username": "string",
  "email": "string",
  "firstName": "string",
  "lastName": "string",
  "image": "string"
}
```

#### Get Owner by ID
```
GET /v1/api/owners/:id
```
Returns a specific owner by their ID.

#### Get Owner by User ID
```
GET /v1/api/owners/user/:userId
```
Returns the owner associated with a specific user ID.

#### Get Owner by Email
```
GET /v1/api/owners/email/:email
```
Returns the owner with the specified email address.

#### Update Owner by ID
```
PUT /v1/api/owners/:id
```
Updates a specific owner by their ID.

#### Delete Owner
```
DELETE /v1/api/owners/:id
```
Deletes an owner and removes the relationship from the associated user.

## Model Methods

### Instance Methods

#### getFullName()
Returns the full name of the owner or fallback values.

```javascript
const owner = await Owner.findById(ownerId);
const fullName = owner.getFullName(); // "John Doe" or "John" or "username" or "Unknown"
```

### Static Methods

#### findByUserId(userId)
Finds an owner by their associated user ID with populated user data.

```javascript
const owner = await Owner.findByUserId(userId);
```

#### findByEmail(email)
Finds an owner by their email address with populated user data.

```javascript
const owner = await Owner.findByEmail('<EMAIL>');
```

## Validation

The Owner module uses comprehensive validation:

### Create Owner Validation
- `userId`: Required, valid MongoDB ObjectId
- `email`: Required, valid email format, normalized to lowercase
- `username`: Optional, 3-30 characters, alphanumeric + underscores only
- `firstName`: Optional, 1-50 characters, trimmed
- `lastName`: Optional, 1-50 characters, trimmed

### Update Owner Validation
- All fields optional
- Same validation rules as create when provided
- `id` parameter must be valid MongoDB ObjectId

## Error Handling

### Registration Errors
- `400`: User already exists
- `400`: Owner with email already exists
- `500`: Owner role not found (requires role initialization)
- `500`: Database transaction errors

### API Errors
- `401`: User not authenticated
- `404`: Owner not found
- `500`: Database or server errors

## Integration with Role System

The Owner module is tightly integrated with the role system:

1. **Automatic Role Assignment**: New users are automatically assigned the "Owner" role
2. **Permission Management**: Owner role has full permissions (View, Edit, Delete, Create)
3. **Role Validation**: Registration fails if Owner role doesn't exist

## Usage Examples

### Creating an Owner (via Registration)
```javascript
// POST /v1/api/auth/register
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "John",
  "lastName": "Doe"
}
```

### Getting Current User's Owner Profile
```javascript
// GET /v1/api/owners/profile
// Headers: Authorization: Bearer <jwt_token>
```

### Updating Owner Profile
```javascript
// PUT /v1/api/owners/profile
{
  "firstName": "Jonathan",
  "lastName": "Smith"
}
```

## Files Structure

### New Files Created
- `src/models/Owner.ts` - Owner Mongoose model
- `src/types/Owner.ts` - Owner TypeScript interfaces
- `src/controller/ownerController.ts` - Owner business logic
- `src/routes/ownerRoutes.ts` - Owner API routes
- `src/validation/ownerValidation.ts` - Owner validation schemas

### Modified Files
- `src/models/User.ts` - Added ownerId field
- `src/types/User.d.ts` - Added ownerId to interface
- `src/controller/authController.ts` - Updated registration process
- `src/app.ts` - Registered owner routes

## Security Considerations

1. **Transaction Safety**: All owner creation uses MongoDB transactions
2. **Authentication Required**: All owner routes require valid authentication
3. **Data Validation**: Comprehensive input validation and sanitization
4. **Email Uniqueness**: Prevents duplicate owners with same email
5. **Role Verification**: Ensures Owner role exists before user creation

## Future Enhancements

1. **Soft Delete**: Implement soft delete for owners instead of hard delete
2. **Audit Trail**: Add audit logging for owner data changes
3. **Bulk Operations**: Support for bulk owner operations
4. **Advanced Search**: Implement search and filtering capabilities
5. **Data Export**: Owner data export functionality for estate planning

## Dependencies

- **mongoose**: MongoDB object modeling
- **express**: Web framework
- **bcryptjs**: Password hashing (inherited from User model)
- **jsonwebtoken**: JWT token handling (inherited from auth system)
- **express-validator**: Input validation

## Testing

To test the Owner module:

1. **Initialize Roles**: Ensure default roles are created
2. **Test Registration**: Verify user and owner creation
3. **Test API Endpoints**: Test all CRUD operations
4. **Test Relationships**: Verify User ↔ Owner relationships
5. **Test Error Handling**: Verify proper error responses

## Troubleshooting

### Common Issues

1. **"Owner role not found"**: Run role initialization endpoint first
2. **"Owner already exists"**: Check for existing owners with same email
3. **Transaction errors**: Ensure MongoDB supports transactions (replica set)
4. **Validation errors**: Check input data format and requirements
