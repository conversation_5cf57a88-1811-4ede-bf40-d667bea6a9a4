import mongoose from 'mongoose';

export interface IOwner extends mongoose.Document {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId; // Reference to User model
  username?: string;
  email: string;
  firstName?: string;
  lastName?: string;
  image?: string;
  googleId?: string;
  externalUser?: boolean;
  subscribedPlanId?: mongoose.Types.ObjectId; // Reference to SubscribedPlan model
  createdAt: Date;
  updatedAt: Date;

  // Instance methods
  getFullName(): string;
}

export interface IOwnerModel extends mongoose.Model<IOwner> {
  // Static methods
  findByUserId(userId: mongoose.Types.ObjectId): Promise<IOwner | null>;
  findByEmail(email: string): Promise<IOwner | null>;
}

export interface ICreateOwnerData {
  userId: mongoose.Types.ObjectId;
  username?: string;
  email: string;
  firstName?: string;
  lastName?: string;
  image?: string;
  googleId?: string;
  externalUser?: boolean;
  subscribedPlanId?: mongoose.Types.ObjectId;
}

export interface IUpdateOwnerData {
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  image?: string;
  subscribedPlanId?: mongoose.Types.ObjectId;
}

