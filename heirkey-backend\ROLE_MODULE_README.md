# Role Module Documentation

## Overview

The Role Module provides a comprehensive role-based access control (RBAC) system for the Heirkey backend application. It supports three main role types: **Owner**, **Nominee**, and **Family**, each with different permission levels.

## Role Types

### 1. Owner
- **Description**: Primary account holder with full access to all features and data
- **Permissions**:
  - `canViewAll` - View all data
  - `canEditAll` - Edit all data
  - `canDeleteAll` - Delete any data
  - `canCreateAll` - Create new data

### 2. Nominee
- **Description**: Designated beneficiary with view-only access to information
- **Permissions**:
  - `canViewAll` - View all data

### 3. Family
- **Description**: Family members with full access to all features and data
- **Permissions**:
  - `canViewAll` - View all data
  - `canEditAll` - Edit all data
  - `canDeleteAll` - Delete any data
  - `canCreateAll` - Create new data

## API Endpoints

### Public Endpoints (No Authentication Required)

#### Initialize Default Roles
```
GET /v1/api/roles/initialize-defaults
```
Creates the default roles (<PERSON><PERSON>, <PERSON>mine<PERSON>, <PERSON>) if they don't exist.

#### Get All Roles
```
GET /v1/api/roles/all?isActive=true
```
Query Parameters:
- `isActive` (optional): Filter by active status (true/false)

#### Get Role by Name
```
GET /v1/api/roles/name/:name
```
Parameters:
- `name`: Role name (Owner, Nominee, Family)

### Protected Endpoints (Authentication Required)

#### Create Role
```
POST /v1/api/roles/
```
Body:
```json
{
  "name": "Owner",
  "description": "Primary account holder with full access",
  "permissions": ["canViewAll", "canEditAll", "canDeleteAll", "canCreateAll"],
  "isActive": true
}
```

#### Get Role by ID
```
GET /v1/api/roles/:id
```

#### Update Role
```
PUT /v1/api/roles/:id
```
Body:
```json
{
  "description": "Updated description",
  "permissions": ["canViewAll", "canEditAll", "canDeleteAll", "canCreateAll"],
  "isActive": true
}
```

#### Delete Role (Soft Delete)
```
DELETE /v1/api/roles/:id
```

#### Assign Role to User
```
POST /v1/api/roles/assign
```
Body:
```json
{
  "userId": "user_object_id",
  "roleId": "role_object_id"
}
```

#### Get Users by Role
```
GET /v1/api/roles/:roleId/users
```

## Database Schema

### Role Schema
```javascript
{
  name: String, // Enum: Owner, Nominee, Family
  description: String,
  permissions: [String],
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

### User Schema (Updated)
```javascript
{
  // ... existing fields
  roleId: ObjectId, // Reference to Role
  // ... existing fields
}
```

## Middleware

### Permission Checking
```javascript
import { checkPermission } from '../middleware/roleMiddleware';

// Protect route with specific permission
router.get('/sensitive-data', checkPermission('canViewAll'), handler);
router.post('/create-data', checkPermission('canCreateAll'), handler);
router.delete('/delete-data/:id', checkPermission('canDeleteAll'), handler);
```

### Role Checking
```javascript
import { checkRole, requireOwner, requireOwnerOrNominee } from '../middleware/roleMiddleware';

// Require specific role
router.post('/admin-action', requireOwner, handler);

// Require Owner or Nominee
router.get('/important-data', requireOwnerOrNominee, handler);
```

### Attach User Role
```javascript
import { attachUserRole } from '../middleware/roleMiddleware';

// Attach role information to request
router.use(attachUserRole);
```

## Usage Examples

### 1. Initialize Default Roles
```bash
curl -X GET http://localhost:3000/v1/api/roles/initialize-defaults
```

### 2. Get All Active Roles
```bash
curl -X GET "http://localhost:3000/v1/api/roles/all?isActive=true"
```

### 3. Assign Owner Role to User
```bash
curl -X POST http://localhost:3000/v1/api/roles/assign \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "userId": "user_id_here",
    "roleId": "owner_role_id_here"
  }'
```

### 4. Protect Route with Permission
```javascript
import { checkPermission } from '../middleware/roleMiddleware';

router.delete('/important-data/:id',
  combinedAuth,
  checkPermission('canDeleteAll'),
  deleteImportantData
);

router.post('/create-category',
  combinedAuth,
  checkPermission('canCreateAll'),
  createCategory
);
```

## Error Responses

### 400 Bad Request
```json
{
  "status": "fail",
  "message": "Validation error",
  "errors": ["Role name is required"]
}
```

### 401 Unauthorized
```json
{
  "status": "fail",
  "message": "Authentication required"
}
```

### 403 Forbidden
```json
{
  "status": "fail",
  "message": "Access denied. Required permission: canCreateAll"
}
```

### 404 Not Found
```json
{
  "status": "fail",
  "message": "Role not found"
}
```

### 500 Internal Server Error
```json
{
  "status": "error",
  "message": "Error creating role",
  "error": "Detailed error message"
}
```

## Files Created/Modified

### New Files
- `src/types/Role.d.ts` - Role type definitions
- `src/models/Role.ts` - Role Mongoose model
- `src/controller/roleController.ts` - Role business logic
- `src/routes/roleRoutes.ts` - Role API routes
- `src/validation/roleValidation.ts` - Role validation schemas
- `src/middleware/roleMiddleware.ts` - Role-based access control middleware

### Modified Files
- `src/models/User.ts` - Added roleId field
- `src/types/User.d.ts` - Added roleId to interface
- `src/app.ts` - Registered role routes

## Next Steps

1. **Test the API endpoints** using the provided examples
2. **Initialize default roles** by calling the initialize endpoint
3. **Assign roles to existing users** using the assign endpoint
4. **Implement role-based access control** in your existing routes using the middleware
5. **Create tests** for the role functionality

## Security Considerations

- Only authenticated users can create, update, or delete roles
- Role assignment requires authentication
- Permission checking is done at the middleware level
- Soft delete is used for roles to maintain data integrity
- Role names are unique and validated
