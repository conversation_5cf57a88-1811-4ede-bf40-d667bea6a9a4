const nodemailer = require("nodemailer");
import { generateEmailVerificationTemplate, generatePasswordResetTemplate, generatePasswordResetSuccessTemplate } from './emailTemplates';

// Helper function to extract reset URL from email text
const extractResetUrl = (text: string): string => {
  const urlMatch = text.match(/http:\/\/[^\s]+/);
  return urlMatch ? urlMatch[0] : 'URL not found';
};

const sendMail = async (options: any) => {
  const transporter = nodemailer.createTransport({
    service: "gmail",
    host: "smtp.gmail.com",
    port: 465,
    secure: true, // true for 465, false for other ports
    auth: {
      user: process.env.STEP_GMAIL,
      pass: process.env.STEP_PASSWORD,
    },
    tls: {
      rejectUnauthorized: false
    }
  });

  const emailOptions = {
    from: `Heirkey support<${process.env.STEP_GMAIL}>`,
    to: options.to,
    subject: options.subject,
    text: options.text,
    html: options.html
  };

  try {
    const result = await transporter.sendMail(emailOptions);
    // console.log('✅ Email sent successfully:', result.messageId);
    return result;
  } catch (error) {
    console.error('❌ Email sending failed:', error);
    
    return { messageId: 'fallback-mode-' + Date.now() };
  }
};

// Send email verification OTP
export const sendEmailVerificationOTP = async (email: string, username: string, otp: string) => {
  try {
    const emailTemplate = generateEmailVerificationTemplate(username, otp);

    const emailOptions = {
      from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
      to: email,
      subject: emailTemplate.subject,
      text: emailTemplate.text,
      html: emailTemplate.html
    };

    const result = await sendMail(emailOptions);
    // console.log('✅ Email verification OTP sent successfully:', result.messageId);
    return result;
  } catch (error) {
    // console.error('❌ Failed to send email verification OTP:', error);
    throw error;
  }
};

// Send password reset email
export const sendPasswordResetEmail = async (email: string, username: string, resetUrl: string) => {
  try {
    const emailTemplate = generatePasswordResetTemplate(username, resetUrl);

    const emailOptions = {
      from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
      to: email,
      subject: emailTemplate.subject,
      text: emailTemplate.text,
      html: emailTemplate.html
    };

    const result = await sendMail(emailOptions);
    // console.log('✅ Password reset email sent successfully:', result.messageId);
    return result;
  } catch (error) {
    // console.error('❌ Failed to send password reset email:', error);
    throw error;
  }
};

// Send password reset success email
export const sendPasswordResetSuccessEmail = async (email: string, username: string) => {
  try {
    const emailTemplate = generatePasswordResetSuccessTemplate(username);

    const emailOptions = {
      from: `Heirkey Support <${process.env.STEP_GMAIL || '<EMAIL>'}>`,
      to: email,
      subject: emailTemplate.subject,
      text: emailTemplate.text,
      html: emailTemplate.html
    };

    const result = await sendMail(emailOptions);
    // console.log('✅ Password reset success email sent successfully:', result.messageId);
    return result;
  } catch (error) {
    // console.error('❌ Failed to send password reset success email:', error);
    throw error;
  }
};

export default sendMail;