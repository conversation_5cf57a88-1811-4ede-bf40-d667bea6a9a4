import express from 'express';
import { googleAuthCallback, googleAuthFailure, profileDetails } from '../controller/googleAuth';
import passport from 'passport';
const router = express.Router();

// Route to initiate Google login
router.get('/v1/auth/google/login', (req, res, next) => {
    passport.authenticate('google', {
        scope: ['profile', 'email'],
        state: 'login',
        session: true
    })(req, res, next);
});

// Route to initiate Google signup
router.get('/v1/auth/google/signup', (req, res, next) => {
    passport.authenticate('google', {
        scope: ['profile', 'email'],
        state: 'signup',
        session: true
    })(req, res, next);
});

// Route to handle the callback from Google
router.get('/v1/auth/google/callback', 
    passport.authenticate('google', { 
        failureRedirect: '/v1/auth/google/failure',
        failureMessage: true,
        session: true 
    }), 
    googleAuthCallback
);

// Handle authentication failure
router.get('/v1/auth/google/failure', (req, res) => {
    const state = req.query.state || 'login';
    const message = (req.session as any)?.messages?.[(req.session as any).messages.length - 1] || 'Authentication failed';
    
    if (state === 'login') {
        return res.redirect(`${process.env.FRONTEND_URL}/auth/google/callback?success=false&error=${encodeURIComponent(message)}&redirectTo=register`);
    }
    res.redirect(`${process.env.FRONTEND_URL}/auth/google/callback?success=false&error=${encodeURIComponent(message)}`);
});

router.get('/v1/profile', profileDetails);

export default router;