# Pricing Plan Duration Update Documentation

## Overview

The Heirkey pricing plan system has been updated to include duration functionality. All plans now have a duration field that specifies how long the plan is valid, with special handling for temporary plans.

## Duration Rules

### Default Duration Rules
- **All Plans**: 1 month duration by default
- **Temporary Plan (`temporary_key`)**: Infinite duration (-1)
- **Spare Key Plan (`spare_key`)**: 1 month duration
- **All Access Key Plan (`all_access_key`)**: 1 month duration

### Duration Values
- **Positive Number**: Duration in months (e.g., 1 = 1 month, 12 = 12 months)
- **-1**: Infinite duration (no expiration)

## Database Schema Changes

### Updated PricingPlan Model
```javascript
{
  type: String,           // 'temporary_key' | 'spare_key' | 'all_access_key'
  price: Number,          // Price in dollars
  displayPrice: String,   // Display price (e.g., "$10/mth", "Free to try")
  tagline: String,        // Plan tagline
  features: [String],     // Array of features
  duration: Number,       // NEW: Duration in months (-1 for infinite)
  active: Boolean,        // Whether plan is active
  createdAt: Date,        // Auto-generated
  updatedAt: Date         // Auto-generated
}
```

### Duration Field Details
- **Type**: Number
- **Required**: Yes
- **Default**: Automatically set based on plan type
  - `temporary_key`: -1 (infinite)
  - Other plans: 1 (1 month)
- **Validation**: Must be -1 or positive number
- **Index**: Not indexed (can be added if needed for queries)

## API Changes

### Create Pricing Plan
**Endpoint**: `POST /v1/api/pricing-plans/`

**Updated Request Body**:
```json
{
  "type": "spare_key",
  "price": 10,
  "displayPrice": "$10/mth",
  "tagline": "Limited access for key holders",
  "features": [
    "Feature 1",
    "Feature 2"
  ],
  "duration": 1,  // NEW: Optional, defaults based on type
  "active": true
}
```

**Response**:
```json
{
  "_id": "plan_id_here",
  "type": "spare_key",
  "price": 10,
  "displayPrice": "$10/mth",
  "tagline": "Limited access for key holders",
  "features": ["Feature 1", "Feature 2"],
  "duration": 1,  // NEW: Duration field included
  "active": true,
  "createdAt": "2025-01-XX...",
  "updatedAt": "2025-01-XX..."
}
```

### Update Pricing Plan
**Endpoint**: `PUT/PATCH /v1/api/pricing-plans/:id`

**Request Body** (all fields optional):
```json
{
  "duration": 3  // Update duration to 3 months
}
```

### Get Pricing Plans
All GET endpoints now return the duration field:

- `GET /v1/api/pricing-plans/` - Get all plans
- `GET /v1/api/pricing-plans/:id` - Get plan by ID
- `GET /v1/api/pricing-plans/type/:type` - Get plan by type

## Default Plans with Duration

### Updated Default Plans
```javascript
[
  {
    type: 'temporary_key',
    price: 0,
    displayPrice: 'Free to try',
    tagline: 'Try Heirkey risk free.',
    features: [
      'You can select one of categories that you would like to share with a designated key holder.',
      'Limited in the information you can share.',
      'The plan lets you try Heirkey\'s feature Risk-free.'
    ],
    duration: -1,  // Infinite duration
    active: true
  },
  {
    type: 'spare_key',
    price: 10,
    displayPrice: '$10/mth',
    tagline: 'Limited access for a key holders',
    features: [
      'You can access and edit your information at any time, but your key holder\'s information is limited.',
      'You key holder can only access your information in the event of the owner\'s incapacity/death.',
      'Provides more privacy for the owner.'
    ],
    duration: 1,  // 1 month duration
    active: true
  },
  {
    type: 'all_access_key',
    price: 12,
    displayPrice: '$12/mth',
    tagline: 'Full access for your key holder.',
    features: [
      'You and your designated key holder can access your information at any time.',
      'Makes it easy for family members to share information.',
      'Great for universal use between family members.'
    ],
    duration: 1,  // 1 month duration
    active: true
  }
]
```

## Validation Rules

### Duration Validation
- **Required**: Yes (automatically set if not provided)
- **Type**: Number
- **Valid Values**: 
  - `-1` (infinite duration)
  - Any positive number (duration in months)
- **Invalid Values**: 
  - `0` (not allowed)
  - Negative numbers except `-1`
  - Non-numeric values

### Validation Error Messages
```json
{
  "message": "Validation error",
  "details": [
    "Duration must be -1 (infinite) or a positive number of months"
  ]
}
```

## Testing the Duration Feature

### 1. Initialize Default Plans
```bash
POST http://localhost:3000/v1/api/pricing-plans/initialize
```

**Expected Response**:
```json
{
  "message": "Default pricing plans created successfully",
  "plans": [
    {
      "_id": "...",
      "type": "temporary_key",
      "duration": -1,  // Infinite
      // ... other fields
    },
    {
      "_id": "...",
      "type": "spare_key", 
      "duration": 1,   // 1 month
      // ... other fields
    },
    {
      "_id": "...",
      "type": "all_access_key",
      "duration": 1,   // 1 month
      // ... other fields
    }
  ]
}
```

### 2. Get All Plans (Verify Duration)
```bash
GET http://localhost:3000/v1/api/pricing-plans/
```

### 3. Create Custom Plan with Duration
```bash
POST http://localhost:3000/v1/api/pricing-plans/
Content-Type: application/json

{
  "type": "custom_plan",
  "price": 15,
  "displayPrice": "$15/mth",
  "tagline": "Custom plan",
  "features": ["Custom feature"],
  "duration": 6  // 6 months
}
```

### 4. Update Plan Duration
```bash
PUT http://localhost:3000/v1/api/pricing-plans/:plan_id
Content-Type: application/json

{
  "duration": 12  // Change to 12 months
}
```

### 5. Test Validation Errors
```bash
POST http://localhost:3000/v1/api/pricing-plans/
Content-Type: application/json

{
  "type": "invalid_plan",
  "price": 10,
  "displayPrice": "$10/mth", 
  "tagline": "Test",
  "features": ["Feature"],
  "duration": 0  // Invalid: should return validation error
}
```

## Migration Notes

### Existing Data
- If you have existing pricing plans without duration, they will need to be updated
- The model default will automatically set duration based on plan type
- Consider running a migration script to update existing records

### Migration Script Example
```javascript
// Update existing plans without duration
await PricingPlan.updateMany(
  { duration: { $exists: false } },
  [
    {
      $set: {
        duration: {
          $cond: {
            if: { $eq: ["$type", "temporary_key"] },
            then: -1,
            else: 1
          }
        }
      }
    }
  ]
);
```

## Frontend Integration

### Display Duration
```javascript
// Helper function to display duration
function formatDuration(duration) {
  if (duration === -1) {
    return "Unlimited";
  }
  return duration === 1 ? "1 month" : `${duration} months`;
}

// Usage
const plan = { duration: -1 };
console.log(formatDuration(plan.duration)); // "Unlimited"

const plan2 = { duration: 3 };
console.log(formatDuration(plan2.duration)); // "3 months"
```

### Plan Selection Logic
```javascript
// Check if plan is temporary (infinite duration)
function isTemporaryPlan(plan) {
  return plan.type === 'temporary_key' || plan.duration === -1;
}

// Calculate plan expiration
function calculateExpiration(startDate, duration) {
  if (duration === -1) {
    return null; // Never expires
  }
  
  const expiration = new Date(startDate);
  expiration.setMonth(expiration.getMonth() + duration);
  return expiration;
}
```

## Files Modified

### Updated Files
- `src/types/PricingPlan.d.ts` - Added duration to interface
- `src/models/PricingPlan.ts` - Added duration field with validation
- `src/validation/pricingPlanValidation.ts` - Added duration validation
- `src/controller/pricingPlanController.ts` - Updated create/update logic and default plans

### No Changes Required
- `src/routes/pricingPlanRoutes.ts` - Routes remain the same
- Database indexes - No new indexes required (can be added if needed)

## Next Steps

1. **Test the API endpoints** to ensure duration functionality works correctly
2. **Update frontend components** to display and handle duration
3. **Consider subscription logic** that uses duration for plan expiration
4. **Add duration-based queries** if needed (e.g., find plans by duration range)
5. **Implement plan expiration logic** in subscription management
