import api from './api';
import { RequestCategoryData } from '../types/requestedCategories';

export const requestCategories = async (data: RequestCategoryData) => {
  const response = await api.post('/v1/api/category-requests/request', data);
  return response.data;
};

export const getMyRequests = async () => {
  const response = await api.get('/v1/api/category-requests/my-requests');
  return response.data;
};

export const getOwnerRequests = async () => {
  const response = await api.get('/v1/api/category-requests/owner-requests');
  return response.data;
};

export const approveRequest = async (token: string, action: 'approve' | 'reject') => {
  const response = await api.post('/v1/api/category-requests/approve', { token, action });
  return response.data;
}; 