name: HRKY-FRONTEND-DEPLOYMENTS-BUILD-PUSH

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Which environment to deploy to?'
        required: true
        type: choice
        options:
          - prod
          - develop

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to EC2 via SSH
        env:
          EC2_IP: ${{ secrets.EC2_PUBLIC_IP }}
          PRIVATE_KEY: ${{ secrets.EC2_PRIVATE_KEY }}
          GITHUB_USERNAME: ${{ secrets.MY_GITHUB_USERNAME }}
          GITHUB_TOKEN: ${{ secrets.MY_GITHUB_TOKEN }}
          BRANCH: ${{ github.event.inputs.environment }}
          VITE_API_URL: ${{ secrets.VITE_API_URL }}
          VITE_BACKEND_URL: ${{ secrets.VITE_BACKEND_URL }}
        run: |
          echo "$PRIVATE_KEY" | tr -d '\r' > private-key.pem
          chmod 400 private-key.pem

          ssh -o StrictHostKeyChecking=no -i private-key.pem ubuntu@$EC2_IP "
            echo '✅ Connected to EC2'

            # Clone repo if not present
            if [ ! -d /home/<USER>/project/hierarchy/frontend/heirkey-frontend ]; then
              git clone https://$GITHUB_USERNAME:$<EMAIL>/myorg/heirkey-frontend.git /home/<USER>/project/hierarchy/frontend/heirkey-frontend
            fi

            cd /home/<USER>/project/hierarchy/frontend/heirkey-frontend

            echo '🔄 Pulling branch: $BRANCH'
            git fetch origin
            git checkout $BRANCH
            git reset --hard origin/$BRANCH
            git clean -fd

            echo '⚙️ Writing .env file...'
            echo \"VITE_API_URL=$VITE_API_URL\" > .env
            echo \"VITE_BACKEND_URL=$VITE_BACKEND_URL\" >> .env

            echo '📦 Installing dependencies...'
            npm install

            echo '🛠️ Building...'
            npm run build

            echo '🚚 Deploying to Nginx directory...'
            sudo mkdir -p /var/www/heirkey-frontend
            sudo cp -r dist/* /var/www/heirkey-frontend/
            sudo chown -R www-data:www-data /var/www/heirkey-frontend/
            sudo chmod -R 755 /var/www/heirkey-frontend/

            echo '🔁 Restarting Nginx...'
            sudo systemctl restart nginx
          "

          rm private-key.pem

      - name: Notify Slack on Success
        if: success()
        run: |
          curl -X POST -H 'Content-type: application/json' \
          --data '{"text":"✅ *HeirKey Frontend Deploy Succeeded!* Branch: `${{ github.event.inputs.environment }}` Commit: `${{ github.sha }}`"}' \
          ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Slack on Failure
        if: failure()
        run: |
          curl -X POST -H 'Content-type: application/json' \
          --data '{"text":"❌ *HeirKey Frontend Deploy Failed!* Branch: `${{ github.event.inputs.environment }}` Commit: `${{ github.sha }}`"}' \
          ${{ secrets.SLACK_WEBHOOK_URL }}
