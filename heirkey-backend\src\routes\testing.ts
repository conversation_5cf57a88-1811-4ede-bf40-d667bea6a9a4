import express from 'express';
import mongoose from 'mongoose';

const router = express.Router()

router.get('/health', (_req, res) => {
    res.status(200).json({
        status: 'ok',
        message: 'server is running....',
        timestamp: new Date(),
        environment: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '1.0.0',
        uptime: process.uptime() + ' seconds'
    });
});

router.get(`/callback`,async(req,res)=>{
    console.log(req.query)
    res.status(200).json("Request recieved")
})


export default router;
