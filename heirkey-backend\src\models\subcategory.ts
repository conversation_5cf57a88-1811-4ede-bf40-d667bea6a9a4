import mongoose, { Schema, Document } from 'mongoose';

interface ISubcategory extends Document {
    name: string;
    categoryId: mongoose.Schema.Types.ObjectId;
}

const SubcategorySchema: Schema = new Schema({
    name: { type: String, required: true },
    categoryId: { type: mongoose.Schema.Types.ObjectId, ref: 'Category', required: true },
}, { timestamps: true });

const Subcategory = mongoose.model<ISubcategory>('Subcategory', SubcategorySchema);
export default Subcategory;