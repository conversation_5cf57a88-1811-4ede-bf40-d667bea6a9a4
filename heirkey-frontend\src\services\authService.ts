import api from './api';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  username: string;
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface User {
  id: string;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  image?: string;
  phone?: string;
  address?: string;
  zipCode?: string;
  country?: string;
  pricingPlan?: string;
  role?: string;
  roleId?: string;
  ownerId?: string; // Reference to Owner model for nominee/family users
}

export interface AuthResponse {
  message: string;
  token?: string;
  user?: User;
  // Email verification fields
  requiresVerification?: boolean;
  email?: string;
  emailError?: boolean;
}

const authService = {
  // Login user
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const response = await api.post('v1/api/auth/login', credentials);
    return response.data as AuthResponse;
  },

  // Register user
  register: async (credentials: RegisterCredentials): Promise<AuthResponse> => {
    const response = await api.post('v1/api/auth/register', credentials);
    return response.data as AuthResponse;
  },

  // Logout user
  logout: async (): Promise<void> => {
    try {
      await api.post('v1/api/auth/logout');
    } finally {
      // Clear local storage regardless of API response
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    }
  },

  // Get current user profile
  getProfile: async (): Promise<User> => {
    const response = await api.get('v1/api/auth/profile');
    return response.data as User;
  },

  // Reset password request
  forgotPassword: async (email: string): Promise<{ message: string }> => {
    const response = await api.post('v1/api/auth/forget-password', { email });
    return response.data as { message: string };
  },

  // Reset password with token
  resetPassword: async (token: string, password: string): Promise<{ message: string }> => {
    const response = await api.post(`v1/api/auth/reset-password/${token}`, { password });
    return response.data as { message: string };
  },

  // Update user profile
  updateProfile: async (userData: Partial<User>): Promise<User> => {
    const response = await api.put('v1/api/auth/profile', userData);
    return response.data as User;
  },

  // Update profile image
  updateProfileImage: async (formData: FormData): Promise<User> => {
    const response = await api.post('v1/api/auth/profile/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data as User;
  },

  // Update user pricing plan
  updatePricingPlan: async (planId: string): Promise<User> => {
    const response = await api.patch('v1/api/pricing-plan', { pricingPlan: planId });
    return response.data as User;
  },
};

export default authService;
