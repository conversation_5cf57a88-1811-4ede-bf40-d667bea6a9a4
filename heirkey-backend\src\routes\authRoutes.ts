import express from 'express';
import { registerUser, loginUser, getAllUsers, userDetails, forgetPassword, getProfile, logout, resetPassword, updateUserProfileImage, updateUserProfile, verifyEmail, resendVerificationOTP, getVerificationStatus } from '../controller/authController';
import { registerValidation, loginValidation, profileUpdateValidation } from '../validation/userValidation';
import { combinedAuth } from '../middleware/authMiddleware';
import upload from '../middleware/multerMiddleware';

const router = express.Router();

// Public routes
router.post('/register', registerValidation, registerUser);
router.post('/login', loginValidation, loginUser);
router.post('/verify-email', verifyEmail);
router.post('/resend-verification', resendVerificationOTP);
router.post('/forget-password', forgetPassword);
router.post('/reset-password/:token', resetPassword);

// Debug routes (development only)
router.get('/debug/verification-status/:email', getVerificationStatus);

// Test email endpoint (remove in production)
//

// Protected routes - using combined authentication
router.get('/profile', combinedAuth, getProfile);
router.put('/profile', combinedAuth, profileUpdateValidation, updateUserProfile);
router.post('/profile/image', combinedAuth, upload.single('image'), updateUserProfileImage);
router.post('/logout', combinedAuth,  logout as express.RequestHandler);

// Admin routes
router.get('/users', combinedAuth, getAllUsers);
router.post('/users', combinedAuth, upload.single('image'), userDetails);

export default router;
