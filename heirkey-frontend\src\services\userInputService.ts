import api from './api';

// Helper function to generate a valid MongoDB ObjectId
// MongoDB ObjectIds are 24-character hex strings
export const generateObjectId = (): string => {
  const timestamp = Math.floor(new Date().getTime() / 1000).toString(16).padStart(8, '0');
  const machineId = Math.floor(Math.random() * 16777216).toString(16).padStart(6, '0');
  const processId = Math.floor(Math.random() * 65536).toString(16).padStart(4, '0');
  const counter = Math.floor(Math.random() * 16777216).toString(16).padStart(6, '0');
  return timestamp + machineId + processId + counter;
};

interface Answer {
  index: number;
  questionId?: string; // MongoDB ObjectId - optional, will be generated by MongoDB
  originalQuestionId: string; // Our manual ID (q1, q2, etc.)
  question: string;
  type: string;
  answer: string;
}

interface SectionAnswers {
  sectionId?: string; // MongoDB ObjectId - optional, will be generated by MongoDB
  originalSectionId: string; // Our manual section ID (101A, 101B, etc.)
  isCompleted: boolean;
  answers: Answer[];
}

interface UserInputData {
  _id?: string; // MongoDB ObjectId for the document
  userId: string; // MongoDB ObjectId
  ownerId?: string; // MongoDB ObjectId - will be populated by backend
  categoryId?: string; // MongoDB ObjectId - will be generated by backend
  originalCategoryId: string; // Our manual category ID (1, 2, etc.)
  subCategoryId?: string; // MongoDB ObjectId - will be generated by backend
  originalSubCategoryId: string; // Our manual subcategory ID (101, 102, etc.)
  answersBySection: SectionAnswers[];
}

// Interface for dashboard stats
interface CategoryStats {
  categoryId: string;
  answeredQuestions: number;
}

/**
 * Service for handling user input operations
 */
const userInputService = {
  /**
   * Creates a new user input record
   * @param data - The user input data to save
   * @returns The created user input record
   */
  createUserInput: async (data: UserInputData) => {
    try {
      // Validate that we have at least one answer
      if (!data.answersBySection || data.answersBySection.length === 0 ||
          data.answersBySection.every(section => !section.answers || section.answers.length === 0)) {
        throw new Error('Cannot save empty answers. Please provide at least one answer.');
      }

      // Data validation and preparation complete

      // Ensure all answers are properly formatted
      const sanitizedData = {
        ...data,
        answersBySection: data.answersBySection.map(section => ({
          ...section,
          originalSectionId: section.originalSectionId,
          isCompleted: true,
          answers: section.answers
            .filter(answer => answer && answer.originalQuestionId) // Filter out invalid answers
            .map(answer => ({
              ...answer,
              index: answer.index || 0,
              type: answer.type === "textarea" ? "text" : (answer.type || "text"), // Convert textarea to text
              answer: String(answer.answer || "").trim()
            }))
        }))
      };

      try {
        const response = await api.post('v1/api/user-inputs', sanitizedData);
        return response.data;
      } catch (apiError) {
        // Try with a direct fetch call as a last resort
        const fetchResponse = await fetch('/v1/api/user-inputs', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(sanitizedData),
        });

        if (!fetchResponse.ok) {
          throw new Error(`API error: ${fetchResponse.status}`);
        }

        const result = await fetchResponse.json();
        return result;
      }
    } catch (error) {
      // Log error in development mode only
      if (process.env.NODE_ENV === 'development') {
        console.error('Error creating user input:', error);
      }
      throw error;
    }
  },

  /**
   * Gets a user input record by ID
   * @param id - The ID of the user input record to retrieve
   * @returns The user input record
   */
  getUserInput: async (id: string) => {
    const response = await api.get(`v1/api/user-inputs/${id}`);
    return response.data;
  },

  /**
   * Gets user input records for a specific user and category
   * @param userId - The ID of the user
   * @param categoryId - The ID of the category
   * @returns Array of user input records
   */
  getUserInputsByUserAndCategory: async (userId: string, categoryId: string) => {
    const response = await api.get('v1/api/user-inputs', {
      params: { userId, categoryId }
    });
    return response.data;
  },

  /**
   * Gets user input records for a specific owner and category
   * @param ownerId - The ID of the owner
   * @param categoryId - The ID of the category
   * @returns Array of user input records
   */
  getUserInputsByOwnerAndCategory: async (ownerId: string, categoryId: string) => {
    const response = await api.get('v1/api/user-inputs/by-owner', {
      params: { ownerId, categoryId }
    });
    return response.data;
  },

  /**
   * Gets user input records for a specific subcategory
   * @param userId - The ID of the user
   * @param originalCategoryId - The original category ID
   * @param originalSubCategoryId - The original subcategory ID
   * @returns Array of filtered user input records
   */
  getUserInputsBySubcategory: async (userId: string, originalCategoryId: string, originalSubCategoryId: string) => {
    const response = await userInputService.getUserInputsByUserAndCategory(userId, originalCategoryId);

    // Ensure we have an array to work with
    const allUserInputs = Array.isArray(response) ? response : [];

    // Filter for the specific subcategory
    return allUserInputs.filter((input: UserInputData) =>
      input.originalSubCategoryId === originalSubCategoryId
    );
  },

  /**
   * Gets user input records by category (used by Redux)
   * Now supports both user-based and owner-based fetching
   * @param userOrOwnerId - The ID of the user or owner
   * @param originalCategoryId - The original category ID
   * @param useOwnerId - Whether to use owner-based fetching (default: false for backward compatibility)
   * @returns Array of user input records
   */
  getUserInputsByCategory: async (userOrOwnerId: string, originalCategoryId: string, useOwnerId: boolean = false) => {
    let response;
    if (useOwnerId) {
      response = await userInputService.getUserInputsByOwnerAndCategory(userOrOwnerId, originalCategoryId);
    } else {
      response = await userInputService.getUserInputsByUserAndCategory(userOrOwnerId, originalCategoryId);
    }
    return Array.isArray(response) ? response : [];
  },

  /**
   * Updates an existing user input record
   * @param id - The ID of the user input record to update
   * @param data - The updated user input data
   * @returns The updated user input record
   */
  updateUserInput: async (id: string, data: Partial<UserInputData>) => {
    try {
      // Validate that we have at least one answer if answersBySection is provided
      if (data.answersBySection &&
          (data.answersBySection.length === 0 ||
           data.answersBySection.every(section => !section.answers || section.answers.length === 0))) {
        throw new Error('Cannot update with empty answers. Please provide at least one answer.');
      }

      // Ensure all answers are properly formatted
      const sanitizedData = data.answersBySection ? {
        ...data,
        answersBySection: data.answersBySection.map(section => ({
          ...section,
          originalSectionId: section.originalSectionId,
          isCompleted: true,
          answers: section.answers
            .filter(answer => answer && answer.originalQuestionId) // Filter out invalid answers
            .map(answer => ({
              ...answer,
              index: answer.index || 0,
              type: answer.type === "textarea" ? "text" : (answer.type || "text"), // Convert textarea to text
              answer: String(answer.answer || "").trim()
            }))
        }))
      } : data;

      try {
        const response = await api.patch(`v1/api/user-inputs/${id}`, sanitizedData);
        return response.data;
      } catch (apiError) {
        // Try with a direct fetch call as a last resort
        const fetchResponse = await fetch(`/v1/api/user-inputs/${id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(sanitizedData),
        });

        if (!fetchResponse.ok) {
          throw new Error(`API error: ${fetchResponse.status}`);
        }

        const result = await fetchResponse.json();
        return result;
      }
    } catch (error) {
      // Log error in development mode only
      if (process.env.NODE_ENV === 'development') {
        console.error('Error updating user input:', error);
      }
      throw error;
    }
  },

  /**
   * Save user input (used by Redux)
   * @param data - The user input data to save
   * @returns The created user input record
   */
  saveUserInput: async (data: UserInputData) => {
    return await userInputService.createUserInput(data);
  },

  /**
   * Gets dashboard stats for an owner
   * @param ownerId - The ID of the owner
   * @returns Array of category stats with answered question counts
   */
  getDashboardStats: async (ownerId: string) => {
    try {
      const response = await api.get('v1/api/user-inputs/dashboard/stats', {
        params: { ownerId }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      // Try with a direct fetch call as a last resort
      try {
        const fetchResponse = await fetch(`/v1/api/user-inputs/dashboard/stats?ownerId=${ownerId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (!fetchResponse.ok) {
          throw new Error(`API error: ${fetchResponse.status}`);
        }

        return await fetchResponse.json();
      } catch (fetchError) {
        console.error('Error with direct fetch for dashboard stats:', fetchError);
        throw fetchError;
      }
    }
  },




};

/**
 * Helper function to convert user input data to form values
 * @param userInput - The user input data to convert
 * @returns An object with question IDs as keys and answers as values
 */
export const convertUserInputToFormValues = (userInput: UserInputData): Record<string, string | string[]> => {
  if (!userInput || !userInput.answersBySection) {
    return {};
  }

  const formValues: Record<string, string | string[]> = {};

  // Process all sections and their answers
  userInput.answersBySection.forEach((section: SectionAnswers) => {
    section.answers.forEach((answer: Answer) => {
      if (answer.originalQuestionId && answer.answer) {
        // For Important Contacts (section 207), keep as array
        if (section.originalSectionId === '207') {
          if (!formValues[answer.originalQuestionId]) {
            formValues[answer.originalQuestionId] = [];
          }
          (formValues[answer.originalQuestionId] as string[]).push(answer.answer);
        } else {
          // For other sections, use string
          formValues[answer.originalQuestionId] = answer.answer;
        }
      }
    });
  });

  return formValues;
};

export default userInputService;
