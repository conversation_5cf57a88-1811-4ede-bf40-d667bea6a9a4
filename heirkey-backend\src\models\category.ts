import mongoose from 'mongoose';

const categorySchema = new mongoose.Schema({
    name: { type: String, required: true },
    image: { type: String },
    numericId: { type: Number, unique: true, sparse: true }
}, { timestamps: true });

// Add text index for name field for text search capabilities
categorySchema.index({ name: 'text' });

const Category = mongoose.model('Category', categorySchema);

export default Category;


