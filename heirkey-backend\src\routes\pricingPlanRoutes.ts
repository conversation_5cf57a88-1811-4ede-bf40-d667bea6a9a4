import express from 'express';
import {
  createPricingPlan,
  getAllPricingPlans,
  getPricingPlanById,
  getPricingPlanByType,
  updatePricingPlan,
  deletePricingPlan,
  initializeDefaultPricingPlans,
  updateAllPlansWithDuration,
  updateAllPlansWithCategoryLimits
} from '../controller/pricingPlanController';
import { validatePricingPlan, validatePricingPlanUpdate } from '../validation/pricingPlanValidation';

const router = express.Router();

// Initialize default pricing plans (should be called once during setup)
router.post('/initialize', initializeDefaultPricingPlans);

// Update all existing plans with duration (migration endpoint)
router.post('/update-duration', updateAllPlansWithDuration);

// Update all existing plans with category limits (migration endpoint)
router.post('/update-category-limits', updateAllPlansWithCategoryLimits);

// Get all pricing plans (with optional active filter)
router.get('/', getAllPricingPlans);

// Get pricing plan by type
router.get('/type/:type', getPricingPlanByType);

// Get pricing plan by ID
router.get('/:id', getPricingPlanById);

// Create new pricing plan
router.post('/', validatePricingPlan, createPricingPlan);

// Update pricing plan
router.patch('/:id', validatePricingPlanUpdate, updatePricingPlan);
router.put('/:id', validatePricingPlanUpdate, updatePricingPlan);

// Delete pricing plan
router.delete('/:id', deletePricingPlan);

export default router;
