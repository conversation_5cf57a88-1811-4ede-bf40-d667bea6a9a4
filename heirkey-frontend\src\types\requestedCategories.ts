import { CategoryId } from '../constants/categories';

export interface RequestedCategory {
  id: string;
  categoryIds: CategoryId[];
  status: 'pending' | 'approved' | 'rejected';
  requestMessage?: string;
  createdAt: string;
  updatedAt: string;
  requester?: {
    name: string;
    email: string;
  };
}

export interface RequestCategoryData {
  categoryIds: CategoryId[];
  message?: string;
}

export interface RequestedCategoriesState {
  requests: RequestedCategory[];
  loading: boolean;
  error: string | null;
  success: string | null;
} 