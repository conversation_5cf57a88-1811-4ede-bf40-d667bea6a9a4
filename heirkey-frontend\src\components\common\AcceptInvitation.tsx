import { useParams, useNavigate } from "react-router-dom";
import { useState } from "react";
import { acceptInvite } from "@/services/inviteService";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function AcceptInvitation() {
  const { token } = useParams<{ token: string }>();
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await acceptInvite(token!, password);
      toast.success("Invitation accepted! You can now log in.");
      navigate("/auth/login");
    } catch (err: any) {
      toast.error(err.response?.data?.message || "Failed to accept invitation");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
      <form onSubmit={handleSubmit} className="bg-white p-8 rounded shadow-md w-full max-w-md">
        <h2 className="text-2xl font-bold mb-4 text-center">Accept Invitation</h2>
        <p className="mb-6 text-center text-gray-600">Set your password to activate your account.</p>
        <Input
          type="password"
          value={password}
          onChange={e => setPassword(e.target.value)}
          required
          placeholder="Enter a password"
          className="mb-4"
        />
        <Button type="submit" className="w-full" disabled={loading}>
          {loading ? "Accepting..." : "Accept Invitation"}
        </Button>
      </form>
    </div>
  );
} 