import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import { IUser } from '../types/User';
import crypto from 'crypto';

const userSchema = new mongoose.Schema({
  username: { type: String },
  email: { type: String },
  password: { type: String },
  firstName: { type: String },
  lastName: { type: String },
  image: { type: String },
  phone: { type: String },
  address: { type: String },
  zipCode: { type: String },
  country: { type: String },
  googleId: { type: String },
  externalUser: { type: Boolean, default: false },
  isEmailVerified: { type: Boolean, default: false },
  emailVerificationToken: String,
  emailVerificationExpire: Date,
  resetPasswordToken: String,
  resetPasswordExpire: Date,
  roleId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Role',
    default: null
  },
  ownerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Owner',
    default: null
  },
}, { timestamps: true });

userSchema.pre('save', async function (next) {
  // Skip password hashing for external users (Google, etc.)
  if (this.externalUser) return next();

  // Only hash password if it's been modified and is not empty
  if (!this.isModified('password') || !this.password) return next();

  try {
    if (typeof this.password !== 'string') {
      return next(new Error('Password must be a string'));
    }
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as mongoose.CallbackError);
  }
});

userSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  // For external users, always return false for password comparison
  if (this.externalUser) return false;

  // If no password is set, return false
  if (!this.password) return false;

  return bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.generateResetToken = function () {
  const token = crypto.randomBytes(32).toString('hex');
  const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

  this.resetPasswordToken = hashedToken;
  this.resetPasswordExpire = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

  return token; // return the raw token (unhashed) to be sent via email
};

userSchema.methods.generateEmailVerificationToken = function () {
  // Generate a 6-digit OTP
  const otp = Math.floor(100000 + Math.random() * 900000).toString();
  const hashedOtp = crypto.createHash('sha256').update(otp).digest('hex');

  this.emailVerificationToken = hashedOtp;
  this.emailVerificationExpire = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

  return otp; // return the raw OTP to be sent via email
};

const User = mongoose.model<IUser>('User', userSchema);
export default User;
