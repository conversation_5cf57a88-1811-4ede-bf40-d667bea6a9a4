import { useRef, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Mail<PERSON>he<PERSON>, ArrowLeft, RefreshCw } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import Header from '@/web/components/Layout/AppHeader';
import Footer from '../Layout/Footer';
import WebLayout from '../Layout/WebLayout';
import { useAuth } from "@/contexts/AuthContext";
import api from '@/services/api';
import authService from '@/services/authService';

export default function VerificationForm() {
  const navigate = useNavigate();
  const location = useLocation();
  const inputsRef = useRef<Array<HTMLInputElement | null>>([]);
  const { user, login } = useAuth();

  // State management
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [resendCooldown, setResendCooldown] = useState(0);

  // Get email from multiple sources
  const urlParams = new URLSearchParams(location.search);
  const emailFromUrl = urlParams.get('email');
  const email = location.state?.email || emailFromUrl || user?.email || '';

  // Debug: Log email source for troubleshooting
  useEffect(() => {
    console.log('=== EMAIL VERIFICATION DEBUG ===');
    console.log('Location state:', location.state);
    console.log('Location state email:', location.state?.email);
    console.log('User email:', user?.email);
    console.log('Final email:', email);
    console.log('Location pathname:', location.pathname);
    console.log('Location search:', location.search);
    console.log('================================');
  }, [location, user, email]);

  // Resend cooldown timer
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => setResendCooldown(resendCooldown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  // Auto-focus first input
  useEffect(() => {
    inputsRef.current[0]?.focus();
  }, []);

  const handleInputChange = (index: number, value: string) => {
    if (value.length > 1) return; // Prevent multiple characters

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setError('');

    // Auto-focus next input
    if (value && index < 5) {
      inputsRef.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputsRef.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pasted = e.clipboardData.getData('Text').slice(0, 6);
    const newOtp = [...otp];

    pasted.split('').forEach((char, idx) => {
      if (idx < 6 && /^\d$/.test(char)) {
        newOtp[idx] = char;
      }
    });

    setOtp(newOtp);

    // Focus the next empty input or the last input
    const nextEmptyIndex = newOtp.findIndex(val => val === '');
    const focusIndex = nextEmptyIndex === -1 ? 5 : nextEmptyIndex;
    inputsRef.current[focusIndex]?.focus();
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Validate email (should be passed from registration)
    if (!email || !email.includes('@')) {
      setError('Email not found. Please go back to registration and try again.');
      return;
    }

    const otpString = otp.join('');
    if (otpString.length !== 6) {
      setError('Please enter the complete 6-digit verification code');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Verify email with OTP
      const verifyResponse = await api.post('/v1/api/auth/verify-email', {
        email,
        otp: otpString
      });

      const response = verifyResponse.data as any;

      if (response.autoLogin && response.token && response.user) {
        // Auto-login: Store token and user data
        localStorage.setItem('token', response.token);
        localStorage.setItem('user', JSON.stringify(response.user));

        setSuccess('Email verified successfully! Logging you in...');

        // Check for subscription status before redirecting
        setTimeout(async () => {
          try {
            // Fetch latest user profile to check for pricing plan
            const latestUser = await authService.getProfile();
            if (!latestUser.pricingPlan) {
              window.location.href = '/auth/subscribe';
            } else {
              window.location.href = '/dashboard';
            }
          } catch (error) {
            console.error('Error fetching user profile:', error);
            // Fallback to subscription page if there's an error
            window.location.href = '/auth/subscribe';
          }
        }, 2000);
      } else {
        // Fallback: redirect to login
        setSuccess('Email verified successfully! Redirecting to login...');
        setTimeout(() => {
          navigate('/auth/login', {
            state: {
              message: 'Email verified successfully! You can now log in.',
              email
            }
          });
        }, 2000);
      }

    } catch (error: any) {
      console.error('Email verification error:', error);
      setError(error.response?.data?.message || 'Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (resendCooldown > 0) return;

    // Validate email before resending
    if (!email || !email.includes('@')) {
      setError('Email not found. Please go back to registration and try again.');
      return;
    }

    setIsResending(true);
    setError('');

    try {
      await api.post('/v1/api/auth/resend-verification', { email });
      setSuccess('Verification code sent successfully!');
      setResendCooldown(60); // 60 second cooldown

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000);

    } catch (error: any) {
      console.error('Resend OTP error:', error);
      setError(error.response?.data?.message || 'Failed to resend verification code');
    } finally {
      setIsResending(false);
    }
  };

  return (
    <WebLayout subHeaderTitle="Verify Email">
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow flex items-center justify-center px-4 sm:px-6 lg:px-8">
          <div className="bg-white border border-gray-200 rounded-xl shadow-md py-4 mb-8 max-w-md w-full space-y-8">
            <div className="flex justify-center">
              <div className="bg-gray-100 p-4 rounded-full">
                <MailCheck className="h-12 w-12 text-gray-600" />
              </div>
            </div>

            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Verify Your Email</h2>
              <p className="text-lg text-gray-600 mb-2">
                We sent a 6-digit verification code to
              </p>
              <p className="text-lg font-medium text-[#22BBCC] mb-8">
                {email || 'your email address'}
              </p>
            </div>

            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {success && (
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <p className="text-sm text-green-600">{success}</p>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* OTP Input */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 text-center">
                  Enter Verification Code
                </label>
                <div className="flex justify-center gap-2 mb-6">
                {Array.from({ length: 6 }).map((_, index) => (
                  <Input
                    key={index}
                    type="text"
                    inputMode="numeric"
                    pattern="[0-9]*"
                    maxLength={1}
                    value={otp[index]}
                    className="w-12 h-12 font-bold text-[#22BBCC] text-center text-xl rounded-lg border-2 border-[#22BBCC] focus:ring-2 focus:ring-[#22BBCC] focus:border-transparent"
                    ref={(el) => {
                      if (el) {
                        inputsRef.current[index] = el;
                      }
                    }}
                    onChange={(e) => handleInputChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(index, e)}
                    onPaste={index === 0 ? handlePaste : undefined}
                    disabled={isLoading}
                  />
                ))}
                </div>
              </div>

              <div className="flex justify-center">
                <Button
                  type="submit"
                  disabled={isLoading || otp.join('').length !== 6}
                  className="w-48 h-12 text-lg bg-[#22BBCC] text-white hover:bg-[#22BBCA] transition-colors duration-200 disabled:opacity-50"
                >
                  {isLoading ? 'Verifying...' : 'Verify Email'}
                </Button>
              </div>
            </form>

            <div className="text-center text-sm text-gray-600">
              Didn't receive the email?{' '}
              <button
                type="button"
                onClick={handleResendOTP}
                disabled={resendCooldown > 0 || isResending}
                className="text-cyan-600 hover:text-cyan-700 hover:underline font-medium disabled:text-gray-400 disabled:no-underline"
              >
                {isResending ? (
                  <span className="inline-flex items-center">
                    <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                    Sending...
                  </span>
                ) : resendCooldown > 0 ? (
                  `Resend in ${resendCooldown}s`
                ) : (
                  'Click to resend'
                )}
              </button>
            </div>

            <div className="flex justify-center items-center">
              <ArrowLeft className="h-5 w-5 mr-2 text-gray-500" />
              <button
                type="button"
                onClick={() => navigate('/auth/login')}
                className="text-sm text-gray-600 hover:text-gray-800 hover:underline font-medium"
              >
                Back to log in
              </button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    </WebLayout>
  );
}
