{"name": "heirkey", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.8.2", "@types/axios": "^0.9.36", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "formik": "^2.4.6", "framer-motion": "^12.11.0", "input-otp": "^1.4.2", "install": "^0.13.0", "lucide-react": "^0.503.0", "next-themes": "^0.4.6", "npm": "^11.3.0", "radix-ui": "^1.4.1", "react": "^19.0.0", "react-device-detect": "^2.2.3", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-icons": "^5.5.0", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "react-resizable-panels": "^3.0.2", "react-router-dom": "^7.5.3", "react-select": "^5.10.1", "react-select-country-list": "^2.2.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwind-variants": "^1.0.0", "tailwindcss-animate": "^1.0.7", "use-media": "^1.5.0", "vaul": "^1.1.2", "yup": "^1.6.1", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/node": "^22.15.3", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}