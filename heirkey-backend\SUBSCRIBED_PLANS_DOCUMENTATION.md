# Subscribed Plans Model Documentation

## Overview

The `SubscribedPlan` model tracks user subscriptions to pricing plans. It maintains a history of plan changes, calculates expiry dates automatically, and provides subscription management functionality.

## Model Structure

### Database Schema
```javascript
{
  planId: ObjectId,              // Reference to current PricingPlan
  ownerId: ObjectId,             // Reference to Owner model
  previousPlans: [ObjectId],     // Array of previous plan IDs
  currentPlan: ObjectId,         // Reference to current PricingPlan (same as planId)
  expiryAt: Date,               // Auto-calculated expiry date
  createdAt: Date,              // Auto-generated
  updatedAt: Date               // Auto-generated
}
```

### Key Features

#### 1. **Automatic Expiry Calculation**
- Expiry date is automatically calculated based on plan duration
- Infinite duration plans (-1) get expiry set to 100 years in future
- Regular plans get expiry = updatedAt + duration (in months)

#### 2. **Plan History Tracking**
- `previousPlans` array stores all previous plan IDs
- When changing plans, current plan is moved to previousPlans
- Maintains complete subscription history

#### 3. **Virtual Properties**
- `isActive`: Boolean indicating if subscription is still valid
- `daysRemaining`: Number of days until expiry (0 if expired)

#### 4. **Unique Constraints**
- One active subscription per owner (ownerId + currentPlan unique index)

## API Endpoints

### Base URL: `/v1/api/subscriptions`

### 1. Create Subscription
**POST** `/`

**Request Body:**
```json
{
  "planId": "64f8a1b2c3d4e5f6a7b8c9d0",
  "ownerId": "64f8a1b2c3d4e5f6a7b8c9d1",
  "currentPlan": "64f8a1b2c3d4e5f6a7b8c9d0"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "subscription": {
      "_id": "64f8a1b2c3d4e5f6a7b8c9d2",
      "planId": "64f8a1b2c3d4e5f6a7b8c9d0",
      "ownerId": {
        "_id": "64f8a1b2c3d4e5f6a7b8c9d1",
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>"
      },
      "currentPlan": {
        "_id": "64f8a1b2c3d4e5f6a7b8c9d0",
        "type": "spare_key",
        "price": 10,
        "displayPrice": "$10/mth",
        "tagline": "Limited access for key holders",
        "duration": 1
      },
      "previousPlans": [],
      "expiryAt": "2025-02-XX...",
      "isActive": true,
      "daysRemaining": 30,
      "createdAt": "2025-01-XX...",
      "updatedAt": "2025-01-XX..."
    }
  }
}
```

### 2. Get All Subscriptions
**GET** `/`

**Query Parameters:**
- `active`: Filter by active status (`true`/`false`)
- `ownerId`: Filter by specific owner ID

**Response:**
```json
{
  "status": "success",
  "results": 2,
  "data": {
    "subscriptions": [
      {
        "_id": "subscription_id_1",
        "ownerId": { ... },
        "currentPlan": { ... },
        "previousPlans": [ ... ],
        "expiryAt": "2025-02-XX...",
        "isActive": true,
        "daysRemaining": 25
      },
      {
        "_id": "subscription_id_2",
        "ownerId": { ... },
        "currentPlan": { ... },
        "previousPlans": [ ... ],
        "expiryAt": "2025-01-XX...",
        "isActive": false,
        "daysRemaining": 0
      }
    ]
  }
}
```

### 3. Get Subscription by ID
**GET** `/:id`

### 4. Get Owner's Subscription
**GET** `/owner/:ownerId`

**Response:**
```json
{
  "status": "success",
  "data": {
    "subscription": { ... },
    "isActive": true,
    "daysRemaining": 25
  }
}
```

### 5. Change/Upgrade Plan
**PUT** `/owner/:ownerId/change-plan`

**Request Body:**
```json
{
  "newPlanId": "64f8a1b2c3d4e5f6a7b8c9d3"
}
```

**What happens:**
1. Current plan is moved to `previousPlans` array
2. `currentPlan` is updated to new plan
3. `expiryAt` is recalculated based on new plan duration
4. Subscription history is preserved

### 6. Renew Subscription
**PUT** `/:id/renew`

**What happens:**
1. Expiry date is recalculated from current date + plan duration
2. Effectively extends the subscription

### 7. Cancel Subscription
**DELETE** `/:id`

**What happens:**
1. Subscription record is deleted from database
2. Owner can create new subscription later

### 8. Get Expired Subscriptions
**GET** `/expired`

**Response:**
```json
{
  "status": "success",
  "results": 3,
  "data": {
    "expiredSubscriptions": [
      {
        "_id": "expired_sub_1",
        "ownerId": { ... },
        "currentPlan": { ... },
        "expiryAt": "2024-12-XX...",
        "isActive": false,
        "daysRemaining": 0
      }
    ]
  }
}
```

## Postman Testing Guide

### Environment Variables
```
base_url: http://localhost:3000
owner_id: (get from owners endpoint)
plan_id: (get from pricing-plans endpoint)
subscription_id: (get after creating subscription)
```

### Test Flow

#### 1. Get Available Plans
```
GET {{base_url}}/v1/api/pricing-plans/
```

#### 2. Get Available Owners
```
GET {{base_url}}/v1/api/owners/
```

#### 3. Create Subscription
```
POST {{base_url}}/v1/api/subscriptions/
Content-Type: application/json

{
  "planId": "{{plan_id}}",
  "ownerId": "{{owner_id}}",
  "currentPlan": "{{plan_id}}"
}
```

#### 4. Get Owner's Subscription
```
GET {{base_url}}/v1/api/subscriptions/owner/{{owner_id}}
```

#### 5. Change Plan
```
PUT {{base_url}}/v1/api/subscriptions/owner/{{owner_id}}/change-plan
Content-Type: application/json

{
  "newPlanId": "{{new_plan_id}}"
}
```

#### 6. Renew Subscription
```
PUT {{base_url}}/v1/api/subscriptions/{{subscription_id}}/renew
```

#### 7. Get All Subscriptions
```
GET {{base_url}}/v1/api/subscriptions/
```

#### 8. Get Active Subscriptions Only
```
GET {{base_url}}/v1/api/subscriptions/?active=true
```

#### 9. Get Expired Subscriptions
```
GET {{base_url}}/v1/api/subscriptions/expired
```

#### 10. Cancel Subscription
```
DELETE {{base_url}}/v1/api/subscriptions/{{subscription_id}}
```

## Business Logic

### Expiry Calculation Examples

#### Temporary Plan (Infinite Duration)
```javascript
// Plan: { type: "temporary_key", duration: -1 }
// Created: 2025-01-15
// Expiry: 2125-01-15 (100 years later)
```

#### Regular Plan (1 Month)
```javascript
// Plan: { type: "spare_key", duration: 1 }
// Created: 2025-01-15
// Expiry: 2025-02-15 (1 month later)
```

#### Custom Plan (6 Months)
```javascript
// Plan: { type: "custom", duration: 6 }
// Created: 2025-01-15
// Expiry: 2025-07-15 (6 months later)
```

### Plan Change Workflow
```javascript
// Initial state
subscription = {
  currentPlan: "spare_key_plan_id",
  previousPlans: []
}

// After changing to all_access_key
subscription = {
  currentPlan: "all_access_key_plan_id",
  previousPlans: ["spare_key_plan_id"]
}

// After changing to custom plan
subscription = {
  currentPlan: "custom_plan_id", 
  previousPlans: ["spare_key_plan_id", "all_access_key_plan_id"]
}
```

## Error Handling

### Common Error Responses

#### 400 - Validation Error
```json
{
  "message": "Validation error",
  "details": [
    "Owner ID must be a valid MongoDB ObjectId"
  ]
}
```

#### 404 - Not Found
```json
{
  "status": "fail",
  "message": "Pricing plan not found"
}
```

#### 400 - Duplicate Subscription
```json
{
  "status": "fail",
  "message": "Owner already has an active subscription. Use change plan endpoint to modify."
}
```

## Database Indexes

The model includes these indexes for optimal performance:
- `{ ownerId: 1 }` - Find subscriptions by owner
- `{ currentPlan: 1 }` - Find subscriptions by plan
- `{ expiryAt: 1 }` - Find expired/expiring subscriptions
- `{ ownerId: 1, currentPlan: 1 }` - Unique constraint (one subscription per owner)

## Files Created

1. `src/types/SubscribedPlan.d.ts` - TypeScript interface
2. `src/models/SubscribedPlan.ts` - Mongoose model with middleware
3. `src/validation/subscribedPlanValidation.ts` - Joi validation schemas
4. `src/controller/subscribedPlanController.ts` - API controllers
5. `src/routes/subscribedPlanRoutes.ts` - Express routes
6. Updated `src/app.ts` - Route registration

## Next Steps

1. **Test all endpoints** using Postman
2. **Create subscription management UI** in frontend
3. **Add payment integration** for plan changes
4. **Implement subscription notifications** for expiring plans
5. **Add subscription analytics** and reporting
