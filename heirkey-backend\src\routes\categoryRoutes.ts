import express from 'express';
import { category, getAllCategories, getCategories, getFullCategoryDataQuestions } from '../controller/categoryController';

const router = express.Router();

router.post('/', category);
router.get('/all', getAllCategories);

// Utility route to ensure default categories exist
// router.get('/ensure-defaults', ensureDefaultCategories);

// Optional routes
router.get('/', getCategories);
router.get('/subcategories', getFullCategoryDataQuestions);
export default router;