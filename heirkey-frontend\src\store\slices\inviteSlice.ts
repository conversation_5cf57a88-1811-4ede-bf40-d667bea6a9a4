import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { InviteForm, InviteResponse } from "@/types/invite.types";
import { sendInvite } from "@/services/inviteService";

interface InviteState {
  loading: boolean;
  error: string | null;
  success: boolean;
}

const initialState: InviteState = {
  loading: false,
  error: null,
  success: false,
};

export const inviteUser = createAsyncThunk<InviteResponse, InviteForm>(
  "invite/inviteUser",
  async (form, { rejectWithValue }) => {
    try {
      return await sendInvite(form);
    } catch (err: any) {
      return rejectWithValue(err.response?.data?.message || "Invite failed");
    }
  }
);

const inviteSlice = createSlice({
  name: "invite",
  initialState,
  reducers: {
    resetInvite: (state) => {
      state.loading = false;
      state.error = null;
      state.success = false;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(inviteUser.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(inviteUser.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(inviteUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { resetInvite } = inviteSlice.actions;
export default inviteSlice.reducer; 