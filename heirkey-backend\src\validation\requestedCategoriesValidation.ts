import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

const requestCategoriesSchema = Joi.object({
    categoryIds: Joi.array()
        .items(Joi.string().pattern(/^[0-9]+$/))
        .min(1)
        .max(10)
        .required()
        .messages({
            'array.min': 'At least one category must be requested',
            'array.max': 'Cannot request more than 10 categories at once',
            'string.pattern.base': 'Category ID must be a numeric string (e.g., "1", "2", "3")'
        }),
    message: Joi.string()
        .optional()
        .max(500)
        .trim()
        .messages({
            'string.max': 'Message cannot exceed 500 characters'
        })
});

const approveRequestSchema = Joi.object({
    token: Joi.string().required(),
    action: Joi.string().valid('approve', 'reject').required()
});

const updateRequestStatusSchema = Joi.object({
    status: Joi.string().valid('pending', 'approved', 'rejected').required()
});

export const requestCategoriesValidation = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
        await requestCategoriesSchema.validateAsync(req.body);
        next();
    } catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
};

export const approveRequestValidation = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
        await approveRequestSchema.validateAsync(req.body);
        next();
    } catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
};

export const updateRequestStatusValidation = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
        await updateRequestStatusSchema.validateAsync(req.body);
        next();
    } catch (error) {
        if (error instanceof Error) {
            res.status(400).json({ message: error.message });
            return;
        }
        res.status(400).json({ message: 'Invalid input' });
    }
};
