import avatar from '@/assets/global/defaultAvatar/defaultImage.jpg';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { categoryTabsConfig } from '@/data/categoryTabsConfig';
import { convertUserInputToFormValues, generateObjectId } from '@/services/userInputService';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  UserInput,
  fetchUserInputs,
  saveUserInput,
  updateUserInput
} from '@/store/slices/importantContactsSlice';
import {
  QuestionItem,
  buildValidationSchema,
  generateInitialValues,
  handleDependentAnswers
} from '@/web/components/Category/FuneralInstructions/FormFields';
import ScrollToQuestion from '@/web/components/Category/ImportantContacts/ScrollToQuestion';
import GoodToKnowBox from '@/web/components/Global/GoodToKnowBox';
import SubCategoryFooterNav from '@/web/components/Global/SubCategoryFooterNav';
import SubCategoryHeader from '@/web/components/Global/SubCategoryHeader';
import SubCategoryTabs from '@/web/components/Global/SubCategoryTabs';
import SubCategoryTitle from '@/web/components/Global/SubCategoryTitle';
import AppHeader from '@/web/components/Layout/AppHeader';
import Footer from '@/web/components/Layout/Footer';
import SearchPanel from '@/web/pages/Global/SearchPanel';
import { Field, FieldArray, Form, Formik, FormikHelpers } from 'formik';
import { Plus, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import * as Yup from 'yup';

const tabs = categoryTabsConfig.importantcontacts;
const subcategoryId = '207C'; // Religious Affiliation subcategory ID

const ReligiousAffiliation = () => {
  const [savedAnswers, setSavedAnswers] = useState<Record<string, any>>({});
  const [existingInputId, setExistingInputId] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const dispatch = useAppDispatch();

  // Get data from Redux store using selectors
  const questions = useAppSelector((state: any) =>
    state.importantContacts.questions['207']?.filter((q: any) => q.sectionId === subcategoryId) || []
  );
  const loading = useAppSelector((state: any) => state.importantContacts.loading);
  const userInputs = useAppSelector((state: any) => state.importantContacts.userInputs);

  // Get the questionId from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const targetQuestionId = queryParams.get('questionId');

  useEffect(() => {
    if (user?.id) {
      dispatch(fetchUserInputs(user.id));
    }
  }, [dispatch, user]);

  useEffect(() => {
    if (userInputs.length > 0 && !loading) {
      const userInput = userInputs.find((input: UserInput) => input.originalSubCategoryId === subcategoryId);
      if (userInput) {
        const formValues: Record<string, any> = convertUserInputToFormValues(userInput);
        const CONTACTS_QUESTION_ID = questions.find((q: any) => q.isContacts)?.id || '';
        if (formValues[CONTACTS_QUESTION_ID] && typeof formValues[CONTACTS_QUESTION_ID] === 'string') {
          try {
            formValues[CONTACTS_QUESTION_ID] = JSON.parse(formValues[CONTACTS_QUESTION_ID]);
          } catch (e) {
            formValues[CONTACTS_QUESTION_ID] = [];
          }
        }
        setSavedAnswers(formValues);
        setExistingInputId(userInput._id || null);
      }
    }
  }, [userInputs, loading]);

  // Scroll to the target question if specified in URL
  useEffect(() => {
    if (!loading && targetQuestionId) {
      setTimeout(() => {
        const element = document.getElementById(`question-${targetQuestionId}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          element.classList.add('bg-yellow-100');
          setTimeout(() => {
            element.classList.remove('bg-yellow-100');
          }, 2000);
        }
      }, 500);
    }
  }, [loading, targetQuestionId]);

  const handleSubmit = async (values: Record<string, any>, { setSubmitting }: FormikHelpers<Record<string, any>>) => {
    try {
      if (!user || !user.id) {
        throw new Error('You must be logged in to save answers');
      }
      const CONTACTS_QUESTION_ID = questions.find((q: any) => q.isContacts)?.id || '';
      const SHOW_CONTACTS_QUESTION_ID = questions.find((q: any) => q.text.toLowerCase().includes('religious affiliation'))?.id || '';
      const answersBySection = questions.reduce((sections: Record<string, Array<{
        index: number;
        originalQuestionId: string;
        question: string;
        type: string;
        answer: any;
      }>>, question: any) => {
        if (!sections[question.sectionId]) {
          sections[question.sectionId] = [];
        }
        let answer = values[question.id];
        if (question.id === CONTACTS_QUESTION_ID && Array.isArray(answer)) {
          answer = answer.filter((c: any) => c && (c.name?.trim() || c.info?.trim()));
          answer = JSON.stringify(answer);
        }
        if (answer !== undefined && answer !== '' && !(Array.isArray(answer) && answer.length === 0)) {
          sections[question.sectionId].push({
            index: sections[question.sectionId].length,
            originalQuestionId: question.id,
            question: question.text,
            type: question.type,
            answer
          });
        }
        return sections;
      }, {});
      const formattedAnswersBySection = Object.entries(answersBySection).map(([sectionId, answers]) => ({
        originalSectionId: sectionId,
        isCompleted: true,
        answers: answers as Array<{
          index: number;
          originalQuestionId: string;
          question: string;
          type: string;
          answer: any;
        }>
      }));
      if (formattedAnswersBySection.length === 0 ||
          formattedAnswersBySection.every(section => section.answers.length === 0)) {
        setSubmitting(false);
        return;
      }
      const userData = {
        userId: user.id,
        categoryId: generateObjectId(),
        originalCategoryId: '5',
        subCategoryId: generateObjectId(),
        originalSubCategoryId: subcategoryId,
        answersBySection: formattedAnswersBySection
      };
      if (existingInputId) {
        await dispatch(updateUserInput({ id: existingInputId, userData })).unwrap();
      } else {
        const result = await dispatch(saveUserInput(userData)).unwrap();
        if (result && result._id) {
          setExistingInputId(result._id);
        }
      }
      setSubmitting(false);
      navigate('/category/importantcontacts/clubs');
    } catch (error: any) {
      console.error('Error saving religious affiliation details:', error);
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        console.error('Error response headers:', error.response.headers);
      } else if (error.request) {
        console.error('Error request:', error.request);
      } else {
        console.error('Error message:', error.message);
      }
      setSubmitting(false);
    }
  };

  if (questions.length === 0 || loading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }

  const validationSchema = buildValidationSchema(questions, Yup);
  const baseInitialValues = generateInitialValues(questions);
  const CONTACTS_QUESTION_ID = questions.find((q: any) => q.isContacts)?.id || '';
  const SHOW_CONTACTS_QUESTION_ID = questions.find((q: any) => q.text.toLowerCase().includes('religious affiliation'))?.id || '';

  // --- Updated initial values logic ---
  let contacts: any[] = [];
  if (userInputs.length > 0 && !loading) {
    // Find the user input for this subcategory
    const userInput = userInputs.find((input: any) => input.originalSubCategoryId === subcategoryId);
    if (userInput) {
      const section = userInput.answersBySection.find((s: any) => s.originalSectionId === subcategoryId);
      if (section) {
        const contactAnswers = section.answers.filter((a: any) => a.originalQuestionId === CONTACTS_QUESTION_ID);
        if (contactAnswers.length === 1) {
          // Web style: one answer, which is an array
          try {
            const parsed = JSON.parse(contactAnswers[0].answer);
            if (Array.isArray(parsed)) {
              contacts = parsed;
            } else if (parsed && typeof parsed === 'object') {
              contacts = [parsed];
            }
          } catch {}
        } else if (contactAnswers.length > 1) {
          // Mobile style: multiple answers, each a contact
          contacts = contactAnswers.map((a: any) => {
            try {
              return JSON.parse(a.answer);
            } catch {
              return null;
            }
          }).filter(Boolean);
        }
      }
    }
  }
  const initialValues = {
    [CONTACTS_QUESTION_ID]: contacts.length > 0 ? contacts : [{ name: '', info: '' }]
  };

  return (
    <div className="flex flex-col pt-20 min-h-screen">
      <AppHeader />
      <SubCategoryHeader
        title="Religious Affiliation"
        backTo="/category/importantcontacts"
        user={{
          name: user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username : 'Guest',
          email: user?.email || '<EMAIL>',
          avatar,
        }}
      />
      <SubCategoryTabs tabs={tabs} />
      <div className="container mx-auto px-6">
        <SubCategoryTitle
          mainCategory="Important Contacts"
          category="Religious Affiliation"
          description="These files contain questions to help you record your religious contacts so they're easy to find later."
        />
      </div>
      <div className="flex-1 container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                enableReinitialize
                onSubmit={handleSubmit}
              >
                {({ values, isSubmitting, isValid, dirty, setValues }) => {
                  useEffect(() => {
                    handleDependentAnswers(values, questions, setValues);
                  }, [values, questions, setValues]);
                  return (
                    <Form>
                      <div className="mt-4">
                        <ScrollToQuestion questions={questions}>
                          {(refs) => (
                            <>
                              {[...questions]
                                .sort((a: any, b: any) => a.order - b.order)
                                .map((question: any) => (
                                  <div
                                    key={question.id}
                                    id={`question-${question.id}`}
                                    ref={(el: HTMLDivElement | null) => {
                                      refs[question.id] = el;
                                    }}
                                    className="p-4 border rounded-lg mb-4"
                                  >
                                    {CONTACTS_QUESTION_ID && question.id === CONTACTS_QUESTION_ID ? (
                                      <FieldArray name={CONTACTS_QUESTION_ID}>
                                        {({ push, remove, form }) => {
                                          const contacts = Array.isArray(form.values[CONTACTS_QUESTION_ID])
                                            ? form.values[CONTACTS_QUESTION_ID]
                                            : [];
                                          return (
                                            <div>
                                              <div className="mb-2 font-medium">{question.text}</div>
                                              {contacts.length === 0 && (
                                                <div className="text-gray-400 mb-2">No contacts added.</div>
                                              )}
                                              {contacts.map((contact: any, idx: number) => (
                                                <div key={idx} className="flex items-center gap-2 mb-2">
                                                  <Field
                                                    name={`${CONTACTS_QUESTION_ID}.${idx}.name`}
                                                    placeholder="Name"
                                                    className="border rounded px-2 py-1 flex-1"
                                                  />
                                                  <Field
                                                    name={`${CONTACTS_QUESTION_ID}.${idx}.info`}
                                                    placeholder="Phone Number"
                                                    className="border rounded px-2 py-1 flex-1"
                                                  />
                                                  <button
                                                    type="button"
                                                    className="text-red-500 hover:text-red-700"
                                                    onClick={() => remove(idx)}
                                                    aria-label="Delete contact"
                                                  >
                                                    <Trash2 size={18} />
                                                  </button>
                                                </div>
                                              ))}
                                              <button
                                                type="button"
                                                className="flex items-center gap-1 text-[#2BCFD5] hover:text-[#19bbb5] font-medium mt-2"
                                                onClick={() => push({ name: '', info: '' })}
                                              >
                                                <Plus size={18} /> Add Contact
                                              </button>
                                            </div>
                                          );
                                        }}
                                      </FieldArray>
                                    ) : (
                                      <QuestionItem question={question} values={values} />
                                    )}
                                  </div>
                                ))}
                            </>
                          )}
                        </ScrollToQuestion>
                        <div className="mt-8 flex justify-end">
                          <Button
                            type="submit"
                            disabled={isSubmitting || !isValid || !dirty}
                            className="bg-[#2BCFD5] hover:bg-[#19bbb5]"
                          >
                            Save & Continue
                          </Button>
                        </div>
                        <GoodToKnowBox
                          title="Editing my Answers"
                          description="Each topic below is a part of your important contacts, with questions to help you provide important information for you and your loved ones. Click any topic to answer the questions at your own pace—we'll save everything for you."
                        />
                        <SubCategoryFooterNav
                          leftLabel="Work"
                          leftTo="/category/importantcontacts/work"
                          rightLabel="Clubs"
                          rightTo="/category/importantcontacts/clubs"
                        />
                      </div>
                    </Form>
                  );
                }}
              </Formik>
            </div>
          </div>
          <div>
            <SearchPanel />
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ReligiousAffiliation;