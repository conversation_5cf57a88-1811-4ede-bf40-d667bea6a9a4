import { Request, Response, RequestHandler } from 'express';
import UserInput from '../models/userInput';
import User from '../models/User';
import mongoose from 'mongoose';

// Interface for dashboard stats
interface CategoryStats {
  categoryId: string;
  answeredQuestions: number;
}

export const createUserInput = async (req: Request, res: Response) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      res.status(400).json({ message: 'userId is required' });
      return;
    }

    // Get the user to find their ownerId
    const user = await User.findById(userId);
    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Add ownerId to the request body if user has one
    const userInputData = {
      ...req.body
    };

    if (user.ownerId) {
      userInputData.ownerId = user.ownerId;
    } else {
      // Log warning for users without owner ID
      console.warn(`User ${userId} does not have an associated owner. Creating UserInput without ownerId.`);
    }

    const userInput = new UserInput(userInputData);
    await userInput.save();
    res.status(201).json(userInput);
  } catch (error) {
    res.status(500).json({ message: 'Error creating user input', error });
  }
};

export const getUserInput = async (req: Request, res: Response) => {
  try {
    const userInput = await UserInput.findById(req.params.id);
    res.status(200).json(userInput);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching user input', error });
  }
};


export const updateUserInput = async (req: Request, res: Response) => {
  try {
    const { userId } = req.body;

    // If userId is provided in the update, ensure ownerId is also set
    let updateData = { ...req.body };

    if (userId) {
      // Get the user to find their ownerId
      const user = await User.findById(userId);
      if (!user) {
        res.status(404).json({ message: 'User not found' });
        return;
      }

      // Add ownerId to the update data if user has one
      if (user.ownerId) {
        updateData.ownerId = user.ownerId;
      }
    }

    const userInput = await UserInput.findByIdAndUpdate(req.params.id, updateData, { new: true });
    res.status(200).json(userInput);
  } catch (error) {
    res.status(500).json({ message: 'Error updating user input', error });
  }
};
export const getUserInputByUserId = async (req: Request, res: Response) => {
  try {
    const { userId, categoryId } = req.query;

    if (!userId || !categoryId) {
      res.status(400).json({ message: 'userId and categoryId are required.' });
      return;
    }

    const userInputs = await UserInput.find({
      userId: new mongoose.Types.ObjectId(userId as string),
      originalCategoryId: categoryId as string 
      // categoryId: new mongoose.Types.ObjectId(categoryId as string)
    });

    res.status(200).json(userInputs);
  } catch (error) {
    res.status(500).json({ error: (error as Error).message });
  }
};



// export const getUserInputsByUserAndCategoryAndSubcategory: RequestHandler = async (req, res) => {
//   try {
//     const { categoryId, subcategoryId, id } = req.query;

//     // const userId = req.user._id
//     //it should decode from protected route

//     console.log(categoryId, subcategoryId, id);

//     if (  !categoryId || !subcategoryId || !id) {
//       res.status(400).json({ message: 'userId, categoryId, and subcategoryId are required.' });
//       return;
//     }

//     const userInputs = await UserInput.findOne({
//       _id:id,
//       // userId: new mongoose.Types.ObjectId(userId as string),
//       categoryId: new mongoose.Types.ObjectId(categoryId as string),
//       subcategoryId: new mongoose.Types.ObjectId(subcategoryId as string)
//     });


//     res.status(200).json(userInputs);
//   } catch (error) {
//     res.status(500).json({ error: (error as Error).message });
//   }
// };

/**
 * Get dashboard stats for a user
 * This endpoint returns the count of answered questions for each category
 * without fetching all the actual question data
 */
// New function to get user inputs by owner ID
export const getUserInputByOwnerId = async (req: Request, res: Response) => {
  try {
    const { ownerId, categoryId } = req.query;

    if (!ownerId || !categoryId) {
      res.status(400).json({ message: 'ownerId and categoryId are required.' });
      return;
    }

    const userInputs = await UserInput.find({
      ownerId: new mongoose.Types.ObjectId(ownerId as string),
      originalCategoryId: categoryId as string
    });

    res.status(200).json(userInputs);
  } catch (error) {
    res.status(500).json({ error: (error as Error).message });
  }
};

/**
 * Get dashboard stats by owner ID
 * This endpoint returns the count of answered questions for each category for an owner
 * Replaces the old user_id based dashboard stats
 */
export const getDashboardStats: RequestHandler = async (req: Request, res: Response) => {
  try {
    const { ownerId } = req.query;

    if (!ownerId) {
      res.status(400).json({ message: 'ownerId is required' });
      return;
    }

    // Get all user inputs for this owner
    const userInputs = await UserInput.find({
      ownerId: new mongoose.Types.ObjectId(ownerId as string)
    });

    // Calculate stats for each category
    const categoryStats: Record<string, CategoryStats> = {};

    // Process each user input
    userInputs.forEach(input => {
      const categoryId = input.originalCategoryId || '';

      if (!categoryStats[categoryId]) {
        categoryStats[categoryId] = {
          categoryId,
          answeredQuestions: 0
        };
      }

      // Count answers in this input
      if (input.answersBySection && Array.isArray(input.answersBySection)) {
        input.answersBySection.forEach(section => {
          if (section.answers && Array.isArray(section.answers)) {
            categoryStats[categoryId].answeredQuestions += section.answers.length;
          }
        });
      }
    });

    // Convert to array for response
    const statsArray = Object.values(categoryStats);

    res.status(200).json(statsArray);
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    res.status(500).json({
      message: 'Error getting dashboard stats',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};


