import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import GradiantHeader from "@/mobile/components/header/gradiantHeader";
import Footer from "@/mobile/components/layout/Footer";
import { useAuth } from '@/contexts/AuthContext';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { fetchUserInputs, selectUserInputs, selectLoading, selectError, selectSubcategories } from '@/store/slices/importantContactsSlice';
import { CheckCircle } from "lucide-react";
import { getCachedOwnerIdFromUser } from '@/utils/ownerUtils';

export default function ContactPage() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const userInputs = useAppSelector(selectUserInputs);
  const loading = useAppSelector(selectLoading);
  const error = useAppSelector(selectError);
  const subcategories = useAppSelector(selectSubcategories);
  const [isLoading, setIsLoading] = useState(true);
  const [localError, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserAnswers = async () => {
      if (!user || !user.id) {
        setError('You must be logged in to view your answers');
        setIsLoading(false);
        return;
      }

      try {
        const ownerId = await getCachedOwnerIdFromUser(user);
        if (!ownerId) {
          throw new Error('No owner ID found for user');
        }

        dispatch(fetchUserInputs(ownerId));
      } catch (error) {
        console.error('Error fetching user inputs:', error);
        setError('Failed to fetch user inputs. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserAnswers();
  }, [dispatch, user]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <GradiantHeader title="Contacts" showAvatar={true} />
        <div className="container mx-auto px-4 py-6 text-center">
          Loading your contacts...
        </div>
      </div>
    );
  }

  if (localError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <GradiantHeader title="Contacts" showAvatar={true} />
        <div className="container mx-auto px-4 py-6 text-center text-red-500">
          {localError}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <GradiantHeader title="Contacts" showAvatar={true} />
      <div className="container mx-auto px-4 py-6 max-w-md">
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-5 mb-6">
          <h2 className="text-lg font-bold text-[#8B5CF6] mb-2">Understanding Topics</h2>
          <p className="text-sm text-gray-600">
            Each topic below is a part of your home documents, with questions to help you provide important information for you and your loved ones. Click any topic to answer the questions at your own pace—we'll save everything for you.
          </p>
        </div>
        <div className="space-y-3">
          {subcategories.map((subcategory) => {
            // Calculate completed questions for this subcategory
            const completedQuestions = userInputs.reduce((count, input) => {
              if (input.originalSubCategoryId === subcategory.id) {
                return count + input.answersBySection.reduce(
                  (sectionCount, section) => sectionCount + section.answers.length, 0
                );
              }
              return count;
            }, 0);

            const totalQuestions = subcategory.questionsCount;
            const isActive = activeTab === subcategory.title;
            const isCompleted = completedQuestions === totalQuestions && totalQuestions > 0;

            return (
              <div
                key={subcategory.id}
                className={
                  "flex items-center justify-between px-4 py-3 rounded-xl border transition-all " +
                  (isActive
                    ? "border-[#2BCFD5] bg-white shadow"
                    : "border-gray-200 bg-gray-50 hover:bg-white")
                }
                style={{ cursor: "pointer" }}
                onClick={() => {
                  setActiveTab(subcategory.title);
                  navigate(`/category/importantcontacts/${subcategory.title.toLowerCase().replace(/[^a-z0-9]/g, '')}`);
                }}
              >
                <span className="font-medium text-gray-900">{subcategory.title}</span>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-gray-500">
                    {completedQuestions}/{totalQuestions} questions
                  </span>
                  {isCompleted && (
                    <>
                      <CheckCircle className="w-5 h-5 text-[#2BCFD5]" />
                      <button
                        className="text-xs text-[#2BCFD5] font-semibold px-2 py-1 rounded hover:underline"
                        onClick={e => {
                          e.stopPropagation();
                          navigate(`/category/importantcontacts/${subcategory.title.toLowerCase().replace(/[^a-z0-9]/g, '')}`);
                        }}
                      >
                        Edit
                      </button>
                    </>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
      <Footer />
    </div>
  );
}
