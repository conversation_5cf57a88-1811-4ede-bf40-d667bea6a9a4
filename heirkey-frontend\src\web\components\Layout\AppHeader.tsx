import logoFooter from "@/assets/webappimage/logo/logoFooter.png"
import { InviteDialog } from "@/components/common/InviteDialog"
import { RequestCategoryDialog } from "@/components/RequestCategories/RequestCategoryDialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useAuth } from "@/contexts/AuthContext"
import { Bell, LogOut, User } from "lucide-react"
import { Link, useNavigate } from "react-router-dom"

export default function AppHeader() {
  const { user, logout, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/auth/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <header className="bg-white text-[#1F4168] shadow-sm fixed top-0 left-0 right-0 z-50 border-b">
      <div className="max-w-7xl mx-auto px-4 py-4 flex items-center h-20 justify-between">
        <div className="flex items-center space-x-8">
          <Link to="/">
            <img src={logoFooter} alt="Heirkey Logo" className="h-12 w-auto" />
          </Link>
          <nav className="flex items-center space-x-6">
            <Link to="/" className="hover:underline font-medium">Home</Link>
            <Link to="/dashboard" className="hover:underline font-medium">Dashboard</Link>
            <Link to="/directory" className="hover:underline font-medium">Directory</Link>
            <Link to="/support" className="hover:underline font-medium">Support</Link>
          </nav>
        </div>
        <div className="flex items-center gap-2">
          {isAuthenticated && (
            <>
              {user?.role === 'Owner' && <InviteDialog />}
              {(user?.role === 'Family' || user?.role === 'Nominee') && <RequestCategoryDialog />}
            </>
          )}
          <Button variant="outline" className="font-medium" onClick={() => navigate('/auth/subscribe')}>Subscribe</Button>

          {isAuthenticated && (
            <>
              <Button
                variant="ghost"
                className="flex items-center gap-2 font-medium"
                onClick={() => navigate('/auth/user-profile')}
              >
                <User className="w-4 h-4 mr-1" /> Profile
              </Button>
              <Button
                variant="ghost"
                className="flex items-center gap-2 text-red-500 hover:text-red-600 font-medium"
                onClick={handleLogout}
              >
                <LogOut className="w-4 h-4 mr-1" /> Logout
              </Button>
            </>
          )}

          <Button variant="ghost" size="icon"><Bell className="w-5 h-5" /></Button>
        </div>
      </div>
    </header>
  )
}
