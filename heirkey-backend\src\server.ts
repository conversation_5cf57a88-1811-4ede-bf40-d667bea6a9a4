import dotenv from 'dotenv';
import connectDB from './config/db';
import app from './app'; 



dotenv.config();

const PORT = process.env.PORT || 3000;
// const PUBLIC_IP = '*************';
const PUBLIC_IP = '*************';

connectDB();

app.listen(Number(PORT), '0.0.0.0', () => {
  console.log(`Server is running on http://localhost:${PORT}`);
  console.log(`Server is running on http://0.0.0.0:${PORT}`);
  console.log(`You can access it at http://${PUBLIC_IP}:${PORT}`);
});
