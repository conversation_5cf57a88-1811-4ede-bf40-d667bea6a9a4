export const CATEGORIES = {
  HOME_INSTRUCTIONS: '1',
  HOME_DOCUMENTS: '2',
  WILL_INSTRUCTIONS: '3',
  FUNERAL_ARRANGEMENTS: '4',
  IMPORTANT_CONTACTS: '5',
  SOCIAL_MEDIA: '6'
} as const;

export const CATEGORY_NAMES = {
  [CATEGORIES.HOME_INSTRUCTIONS]: 'Home Instructions',
  [CATEGORIES.HOME_DOCUMENTS]: 'Home Documents',
  [CATEGORIES.WILL_INSTRUCTIONS]: 'Will Instructions',
  [CATEGORIES.FUNERAL_ARRANGEMENTS]: 'Funeral Arrangements',
  [CATEGORIES.IMPORTANT_CONTACTS]: 'Important Contacts',
  [CATEGORIES.SOCIAL_MEDIA]: 'Social Media'
} as const;

export type CategoryId = typeof CATEGORIES[keyof typeof CATEGORIES];
export type CategoryName = typeof CATEGORY_NAMES[keyof typeof CATEGORY_NAMES];

export const getCategoryName = (categoryId: CategoryId): CategoryName => {
  return CATEGORY_NAMES[categoryId];
}; 